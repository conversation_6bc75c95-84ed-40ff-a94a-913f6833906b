import logger from '../utils/logger';

/**
 * Webhook configuration for Plaid transfers
 * This centralizes all webhook-related settings
 */
export const WEBHOOK_CONFIG = {
  // Base URL for webhooks - should be your production domain
  BASE_URL: process.env.WEBHOOK_BASE_URL || process.env.APP_BASE_URL || 'https://your-app.com',
  
  // Webhook endpoint path
  WEBHOOK_PATH: '/api/plaid/webhook',
  
  // Full webhook URL
  get FULL_URL(): string {
    return `${this.BASE_URL}${this.WEBHOOK_PATH}`;
  },
  
  // Webhook secret for signature verification
  SECRET: process.env.PLAID_WEBHOOK_SECRET,
  
  // Environment check
  get IS_PRODUCTION(): boolean {
    return process.env.NODE_ENV === 'production';
  },
  
  // Validate webhook configuration
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!this.BASE_URL || this.BASE_URL === 'https://your-app.com') {
      errors.push('WEBHOOK_BASE_URL is not configured. Please set it to your production domain.');
    }
    
    if (!this.SECRET && this.IS_PRODUCTION) {
      errors.push('PLAID_WEBHOOK_SECRET is not configured for production.');
    }
    
    if (!this.BASE_URL.startsWith('https://')) {
      errors.push('WEBHOOK_BASE_URL must use HTTPS for production.');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  },
  
  // Log current webhook configuration
  logConfig(): void {
    const validation = this.validate();
    
    logger.info('Webhook configuration', {
      baseUrl: this.BASE_URL,
      fullUrl: this.FULL_URL,
      hasSecret: !!this.SECRET,
      isProduction: this.IS_PRODUCTION,
      isValid: validation.isValid
    });
    
    if (!validation.isValid) {
      logger.warn('Webhook configuration issues', {
        errors: validation.errors
      });
    }
  }
};

/**
 * Get webhook URL for Plaid transfers
 */
export function getWebhookUrl(): string {
  return WEBHOOK_CONFIG.FULL_URL;
}

/**
 * Validate webhook configuration on startup
 */
export function validateWebhookConfig(): void {
  WEBHOOK_CONFIG.logConfig();
  
  const validation = WEBHOOK_CONFIG.validate();
  if (!validation.isValid) {
    logger.error('Webhook configuration is invalid', {
      errors: validation.errors
    });
    
    if (WEBHOOK_CONFIG.IS_PRODUCTION) {
      throw new Error(`Webhook configuration errors: ${validation.errors.join(', ')}`);
    }
  }
} 