import { Request, Response } from 'express';
import { getPayments } from '../services/paymentService';
import logger from '../utils/logger';
import { sendSuccess, sendError, sendRateLimitExceeded, sendUserSuccess, sendUserError, sendUnauthorized } from '../utils/response';
import { fetchFromApi } from '../services/apiServices';
import { sendReminderEmail, sendWelcomeEmail } from '../services/emailService';
import { executeSecondaryQuery, executeSecondaryQuerySingle } from '../utils/database';
import {
  createPaymentsLoadingSkeleton,
  sendLoadingResponse,
  LOADING_MESSAGES,
  transformToSkeletonStructure
} from '../utils/loadingStates';
import { Console } from 'console';

// Simple in-memory rate limiter
const requestCounts: { [ip: string]: number } = {};
const MAX_REQUESTS_PER_MINUTE = 100;

export const getAllPayments = async (req: Request, res: Response) => {
  try {
    // Rate limiting
    const ip = req.ip || 'unknown'; // Handle undefined IP
    requestCounts[ip] = (requestCounts[ip] || 0) + 1;

    if (requestCounts[ip] > MAX_REQUESTS_PER_MINUTE) {
      logger.warn(`Rate limit exceeded for IP: ${ip}`);
      return sendUserError(res, 'SYSTEM', 'RATE_LIMITED', 429);
    }

    setTimeout(() => {
      requestCounts[ip]--;
    }, 60000); // Reset count after 1 minute

    // Input validation (example)
    const amount = req.query.amount as string;
    if (amount && isNaN(Number(amount))) {
      logger.error(`Invalid amount: ${amount}`);
      return sendUserError(res, 'MONEY', 'INVALID_AMOUNT', 400);
    }

    const payments = await getPayments();
    sendUserSuccess(res, 'TRANSACTIONS', 'RETRIEVED', payments);
  } catch (err) {
    logger.error('Error getting payments:', err);
    sendError(res, 'Failed to retrieve payments', 500);
  }
};

export const getAwaitingPayments = async (
  req: Request & { userId?: string; team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;

    // const { game_id, season_id,group_id} = req.body



    console.log(userId, 'userId')

    const response = await fetchFromApi(`/get-awaitings/${userId}`, 'POST', req.body);

    sendSuccess(res, response.data, 'Awaiting payments retrieved successfully');
  } catch (error) {
    logger.error('Error getting awaiting payments', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve awaiting payments', 500);
  }
};

export const getAllPaymentTotals = async (
  req: Request & { team_connect_user_id?: string },
  res: Response
) => {
  const id = req.team_connect_user_id;

  if (!id) {
    return res.status(400).json({
      error: true,
      status: 400,
      msg: "User ID is required.",
    });
  }

  const p_user_id = id;
const dataQuery = `
  SELECT
    JSON_OBJECT(
        'game_id', g.id,
        'season_id', s.id,
        'game_group_id', ggm.id
    ) AS resolved_ids,

    t.id AS team_id,
    t.id,
    t.team_name,
    t.team_logo,
    COUNT(DISTINCT tm.id) AS player_id,
    s.season_name,
    sp.final_day_of_payment AS end_date,
    sports.sports_name,
    sports.players_per_team,
    g.game_title,
    g.id AS game_id,

    IF(ggm.group_teams IS NULL OR ggm.group_teams = '', 0,
        LENGTH(REPLACE(REPLACE(REPLACE(ggm.group_teams, '[', ''), ']', ''), '"', '')) -
        LENGTH(REPLACE(REPLACE(REPLACE(REPLACE(ggm.group_teams, '[', ''), ']', ''), '"', ''), ',', '')) + 1
    ) AS teamCount

  FROM seasons s
  JOIN games g ON g.id = s.game_id AND g.user_id = ? AND g.status_id NOT IN (3, 28)
  JOIN game_groups_matches ggm ON ggm.season_id = s.id AND ggm.status_id NOT IN (3, 28)
  JOIN sports ON g.sport_id = sports.id
  JOIN season_plans sp ON sp.season_id = s.id
  JOIN teams t ON FIND_IN_SET(t.id, REPLACE(REPLACE(REPLACE(ggm.group_teams, '[', ''), ']', ''), '"', '')) > 0
  LEFT JOIN team_members tm ON tm.team_id = t.id AND tm.role_id = 4
  LEFT JOIN (
      SELECT od.season_id, SUM(od.teams_count) AS teams_count
      FROM order_details od
      JOIN orders o ON o.id = od.order_id
      JOIN payment_statuses ps ON o.payment_status_id = ps.id
      WHERE o.user_id = ? AND ps.payment_status = 'Paid'
      GROUP BY od.season_id
  ) paid_teams_count ON s.id = paid_teams_count.season_id

  GROUP BY
      s.id, s.season_name, ggm.id, t.id, t.team_name, t.team_logo,
      sp.final_day_of_payment, sports.sports_name, sports.players_per_team,
      g.game_title, g.id,t.id
  ORDER BY s.id DESC
;
`;

  

  try {
    // Execute all three queries concurrently using Promise.all
      let totalReceivedAmount = 0;
    let totalOverdueAmount = 0;
    let totalPendingAmount = 0;
    const dataRows = await executeSecondaryQuery(dataQuery, [p_user_id, p_user_id]);
 const data = dataRows
    const enrichedData = await Promise.all(
      data.map(async (team) => {
        const { season_id } = team.resolved_ids;

        // Step 1: Find the season plan (future first, then past)
        let seasonPlan = null;

        const futurePlan = await executeSecondaryQuery(
          `SELECT id AS season_plan_id, price, plan_date, final_day_of_payment
           FROM season_plans
           WHERE season_id = ? AND plan_date > CURDATE()
           ORDER BY plan_date ASC LIMIT 1`,
          [season_id]
        );

        if (futurePlan.length > 0) {
          seasonPlan = futurePlan[0];
        } else {
          const pastPlan = await executeSecondaryQuery(
            `SELECT id AS season_plan_id, price, plan_date, final_day_of_payment
             FROM season_plans
             WHERE season_id = ? AND plan_date <= CURDATE()
             ORDER BY plan_date DESC LIMIT 1`,
            [season_id]
          );

          if (pastPlan.length > 0) {
            seasonPlan = pastPlan[0];
          }
        }

        const playerFee = parseFloat(seasonPlan?.price || 0);

        // Step 2: Get paid amount for the team
        const [result] = await executeSecondaryQuery(
          `
          SELECT
            COUNT(DISTINCT tm.user_id) AS paid_players,
            COALESCE(SUM(ptp.player_charged_amount), 0) AS paid_amount
          FROM team_members tm
          LEFT JOIN player_team_payment ptp ON ptp.player_id = tm.user_id
          WHERE tm.team_id = ? AND tm.role_id = 4 AND tm.is_paid = 1
          `,
          [team.team_id]
        );

        const paid_players = result?.paid_players || 0;
        const paid_amount_for_team = parseFloat(result?.paid_amount || 0); // Keep as number for calculation

        // Calculate total expected for this specific team, and its remaining amount
        const team_expected_amount_total = team.players_per_team * playerFee;
        const remaining_amount_for_team = team_expected_amount_total - paid_amount_for_team;

        // Step 3: Determine payment status and contribute to overall totals
        let paid_status = "Paid"; // default
        const today = new Date();
        const planDate = seasonPlan?.plan_date ? new Date(seasonPlan.plan_date) : null;

        // Add to total received amount
        totalReceivedAmount += paid_amount_for_team; // Accumulate global total received

        if (remaining_amount_for_team > 0) { // Only if there's an outstanding amount
          if (planDate && today < planDate) {
            paid_status = "Pending";
            totalPendingAmount += remaining_amount_for_team; // Accumulate global total pending
          } else if (planDate && today >= planDate) {
            paid_status = "Overdue";
            totalOverdueAmount += remaining_amount_for_team; // Accumulate global total overdue
          }
        }

        // Return the enriched team object (though we only care about totals here)
        return {
          ...team, // Include original team properties if needed elsewhere
          amount: playerFee,
          expected_amount: team_expected_amount_total.toFixed(2),
          paid_amount: paid_amount_for_team.toFixed(2),
          remaining_amount: remaining_amount_for_team.toFixed(2),
          paid_players,
          status: paid_status,
        };
      })
    );

    // After all async operations in map are complete and totals are accumulated
    sendSuccess(res, {
      totalReceivedAmount: totalReceivedAmount.toFixed(2),
      totalOverdueAmount: totalOverdueAmount.toFixed(2),
      totalPendingAmount: totalPendingAmount.toFixed(2)
    }, 'Payment totals retrieved successfully');
    
  } catch (error) {
    console.error(error);
    return res.status(500).json({
      error: true,
      status: 500,
      msg: "Internal Server Error!",
      errors: error,
    });
  }
};
export const getAwaitingPaymentsbyTeamId = async (
  req: Request & { userId?: string; team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.params.id;
    // if (!userId) {
    //   return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    // }


    console.log(userId, 'userId')

    const response = await fetchFromApi(`/get-team/${userId}`, 'POST');

    sendSuccess(res, response.data, 'payments retrieved successfully');
  } catch (error) {
    logger.error('Error getting awaiting payments', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve awaiting payments', 500);
  }
};

export const getAwaitingFitersbyUserId = async (
  req: Request & { userId?: string; team_connect_user_id?: string },
  res: Response
) => {
  try {
 const userId = req.team_connect_user_id;    // if (!userId) {
    //   return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    // }


    console.log(userId, 'userId')

    const response = await fetchFromApi(`/get-filters/${userId}`, 'GET');

    sendSuccess(res, response.data, 'payments retrieved successfully');
  } catch (error) {
    logger.error('Error getting awaiting payments', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve awaiting payments', 500);
  }
};


export const sendReminderForPaymentPending = async (
  req: Request & { userId?: string; team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.params.id;
    // if (!userId) {
    //   return sendUserError(res, 'AUTH', 'MISSING_USER_ID', 400);
    // }



    // const response = await fetchFromApi(`/get-team/${userId}`, 'POST');
const welcomeEmailSent = await sendReminderEmail(req.body)
    sendSuccess(res, welcomeEmailSent, 'payments retrieved successfully');
  } catch (error) {
    logger.error('Error getting awaiting payments', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve awaiting payments', 500);
  }
};

export const getMyDues = async (
  req: Request & { userId?: string; team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendError(res, 'User ID is required', 400);
    }

    logger.info('Getting my dues', { userId });

    // Get query parameters for filtering
    const { status, search, limit, offset } = req.query;
    const limitNum = limit ? parseInt(limit as string) : 50;
    const offsetNum = offset ? parseInt(offset as string) : 0;
    let query = `
         SELECT 
          u.id,
          u.firstname,
          u.email,
          u.status_id,
          u.enabled,
          CASE
        WHEN ptp.payment_status = 'paid' THEN 'Paid'
        WHEN sp.final_day_of_payment < CURDATE() THEN 'Overdue'
        ELSE 'Pending'
      END AS status,
          s.id AS season_id,
          s.season_name,
           s.end_date,
          g.game_title,
          us.match_id,
          CASE 
            WHEN pw.wage_unit_id = 1 THEN pw.amount
            WHEN pw.wage_unit_id IS NOT NULL THEN pw.amount * IFNULL(pw.unit_count, 0)
            ELSE NULL
          END AS amount
        FROM match_staff us
        JOIN users u ON u.id = us.user_id 
        JOIN user_roles ur ON ur.user_id = u.id AND ur.role_id = 5
        JOIN matches m ON m.id = us.match_id
        JOIN game_groups_matches ggm ON m.game_group_id = ggm.id
        JOIN seasons s ON s.id = ggm.season_id
        JOIN games g ON g.id = ggm.game_id
           JOIN season_plans sp ON sp.season_id = s.id
          JOIN paywages pw ON pw.user_id = u.id
          LEFT JOIN player_team_payment ptp ON ptp.player_id = u.id AND ptp.season_id = s.id
        WHERE g.user_id = ?
        GROUP BY
          u.id,
          u.firstname,
          u.email,
          u.status_id,
          u.enabled,
          s.id,
          s.season_name,
          g.game_title,
          us.match_id,
          pw.wage_unit_id,
          pw.amount,
          pw.unit_count,
          sp.final_day_of_payment,
          ptp.payment_status `;
const havingConditions: string[] = [];

if (status === 'Paid' || status === 'Pending' || status === 'Overdue') {
  havingConditions.push(`status = '${status}'`);
}
if (search) {
  const likeTerm = `%${search}%`;
  havingConditions.push(`(
    firstname LIKE '${likeTerm}' OR 
    email LIKE '${likeTerm}' 
  )`);
}



if (havingConditions.length > 0) {
  query += ` HAVING ` + havingConditions.join(' AND ');
}

    const rows = await executeSecondaryQuery(query, [userId]);

    // Format response to match frontend expectations
    const responseData = {
      data: rows,
      totalRecords: rows.length,
      page: 1,
      pageSize: rows.length
    };

    sendSuccess(res, responseData, 'My dues retrieved successfully');
  } catch (error) {
    logger.error('Error getting my dues', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve my dues', 500);
  }
};

/**
 * Get dues summary for dashboard
 * GET /api/payments/get-dues-summary
 */
export const getDuesSummary = async (
  req: Request & { userId?: string; team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendError(res, 'User ID is required', 400);
    }

    // Check if client wants loading state first
    const includeLoading = req.query.loading === 'true';
    if (includeLoading) {
      const skeleton = createPaymentsLoadingSkeleton();
      skeleton.loading.loadingMessage = LOADING_MESSAGES.PAYMENTS.SUMMARY;
      return sendLoadingResponse(res, skeleton, LOADING_MESSAGES.PAYMENTS.SUMMARY);
    }

    logger.info('Getting dues summary', { userId });

    // Get all dues for the user
    const query = `
     SELECT
      u.id,
      u.firstname,
      u.email,
      u.status_id,
      u.enabled,
      CASE
        WHEN ptp.payment_status = 'paid' THEN 'Paid'
        WHEN sp.final_day_of_payment < CURDATE() THEN 'Overdue'
        ELSE 'Pending'
      END AS status,
      s.id AS season_id,
      s.season_name,
      s.end_date,
      g.game_title,
      us.match_id,
      CASE
        WHEN pw.wage_unit_id = 1 THEN pw.amount
        WHEN pw.wage_unit_id IS NOT NULL THEN pw.amount * IFNULL(pw.unit_count, 0)
        ELSE 100.00
      END AS amount,
      sp.final_day_of_payment AS end_date,
      us.created_at,
      ptp.payment_date
    FROM match_staff us
    JOIN users u ON u.id = us.user_id
    JOIN user_roles ur ON ur.user_id = u.id AND ur.role_id = 5
    JOIN matches m ON m.id = us.match_id
    JOIN game_groups_matches ggm ON m.game_group_id = ggm.id
    JOIN seasons s ON s.id = ggm.season_id
    JOIN games g ON g.id = ggm.game_id
    JOIN season_plans sp ON sp.season_id = s.id
     JOIN paywages pw ON pw.user_id = u.id
    LEFT JOIN player_team_payment ptp ON ptp.player_id = u.id AND ptp.season_id = s.id
    WHERE g.user_id = ?
    GROUP BY
      u.id, s.id, g.id, us.match_id, pw.wage_unit_id, pw.amount, pw.unit_count, sp.final_day_of_payment, ptp.payment_status, ptp.payment_date, us.created_at
    ORDER BY s.id DESC, us.created_at DESC
    `;

    const dues = await executeSecondaryQuery(query, [userId]);

    // Calculate summary statistics
    const  totalDues = dues.length;
    const paidDues = dues.filter((due: any) => due.status === 'Paid').length;
    const pendingDues = dues.filter((due: any) => due.status === 'Pending').length;
    const overdueDues = dues.filter((due: any) => due.status === 'Overdue').length;

    const totalAmountDue = dues
      .filter((due: any) => due.status !== 'Paid')
      .reduce((sum: number, due: any) => sum + (parseFloat(due.amount) || 0), 0);

    const totalAmountPaid = dues
      .filter((due: any) => due.status === 'Paid')
      .reduce((sum: number, due: any) => sum + (parseFloat(due.amount) || 0), 0);

    // Get recent payments (last 5 paid dues)
    const recentPayments = dues
      .filter((due: any) => due.status === 'Paid' && due.payment_date)
      .sort((a: any, b: any) => new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime())
      .slice(0, 5)
      .map((due: any) => ({
        id: due.id,
        firstname: due.firstname,
        game_title: due.game_title,
        season_name: due.season_name,
        amount: parseFloat(due.amount) || 0,
        payment_date: due.payment_date,
        status: due.status
      }));

    const summary = {
      totalDues,
      paidDues,
      pendingDues,
      overdueDues,
      totalAmountDue: Math.round(totalAmountDue * 100) / 100, // Round to 2 decimal places
      totalAmountPaid: Math.round(totalAmountPaid * 100) / 100,
      recentPayments,
      // Add structured summary cards data for frontend
      summaryCards: [
        {
          value: totalDues,
          trend: {
            direction: 'neutral' as const,
            value: '0%'
          }
        },
        {
          value: paidDues,
          trend: {
            direction: paidDues > pendingDues ? 'up' as const : 'neutral' as const,
            value: totalDues > 0 ? `${Math.round((paidDues / totalDues) * 100)}%` : '0%'
          }
        },
        {
          value: pendingDues,
          trend: {
            direction: pendingDues > 0 ? 'down' as const : 'neutral' as const,
            value: totalDues > 0 ? `${Math.round((pendingDues / totalDues) * 100)}%` : '0%'
          }
        },
        {
          value: `$${Math.round(totalAmountDue * 100) / 100}`,
          trend: {
            direction: 'neutral' as const,
            value: '0%'
          }
        }
      ],
      recentItems: recentPayments
    };

    console.log('Dues summary:', summary);

    logger.info('Successfully calculated dues summary', { userId, summary: { ...summary, recentPayments: summary.recentPayments.length } });

    sendSuccess(res, summary, 'Dues summary retrieved successfully');
  } catch (error) {
    logger.error('Error getting dues summary', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve dues summary', 500);
  }
};

export const getPlatformDues = async (
  req: Request & { userId?: string; team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendError(res, 'User ID is required', 400);
    }

    logger.info('Getting platform dues', { userId });

    // Parse query params
    const { status, search } = req.query;
    const pageSize = parseInt(req.query.pageSize as string) || 5;
    const page = parseInt(req.query.page as string) || 1;
    const offset = (page - 1) * pageSize;

    // Base query
    let baseQuery = `
      SELECT 
        s.id AS season_id,
        g.game_title AS league_name,
        s.season_name,
        ggm.group_name AS division_name,
        ggm.id,
        COUNT(DISTINCT t.id) AS teams_count,
        ROUND(SUM(
          CASE 
            WHEN ot.team_id IS NOT NULL THEN 
              o.team_charge_paid + (o.team_charge_paid * ((o.tax_paid + o.transaction_charge_paid) / 100))
            ELSE 0
          END
        ) , 2) AS amount_paid,
        ROUND((
          COUNT(DISTINCT t.id) - COUNT(DISTINCT ot.team_id)
        ) * (
          pm.team_charge + (pm.team_charge * ((pm.tax + pm.platform_charge + pm.transaction_charges + pm.stripe_charges) / 100))
        ) , 2) AS amount_pending,
        ROUND(
          SUM(
            CASE 
              WHEN ot.team_id IS NOT NULL THEN 
                o.team_charge_paid + (o.team_charge_paid * ((o.tax_paid + o.transaction_charge_paid) / 100))
              ELSE 0
            END
          ) 
          + 
          (
            COUNT(DISTINCT t.id) - COUNT(DISTINCT ot.team_id)
          ) * (
            pm.team_charge + (pm.team_charge * ((pm.tax + pm.platform_charge + pm.transaction_charges + pm.stripe_charges) / 100))
          ), 2
        )  AS total_amount,
        s.start_date AS due_date,
        CASE 
          WHEN (COUNT(DISTINCT t.id) - COUNT(DISTINCT ot.team_id)) = 0 THEN 'Paid'
          ELSE 'Pending'
        END AS status,
        MAX(ggm.created_at) AS created_at

      FROM games g
      JOIN seasons s ON s.game_id = g.id
      JOIN game_groups_matches ggm ON ggm.season_id = s.id
      LEFT JOIN teams t ON FIND_IN_SET(t.id, ggm.group_teams)
      LEFT JOIN order_teams ot ON ot.team_id = t.id AND ot.group_id = ggm.id AND ot.season_id = s.id
      LEFT JOIN orders o ON o.id = ot.order_id
      JOIN (
        SELECT *
        FROM price_master
        WHERE status_id = 1 AND enabled = 1
        ORDER BY created_at DESC
        LIMIT 1
      ) pm
      WHERE g.user_id = ?
      GROUP BY 
        s.id, ggm.id, g.game_title, s.season_name, ggm.group_name, 
        s.start_date, pm.team_charge, pm.tax, pm.platform_charge, 
        pm.transaction_charges, pm.stripe_charges
    `;

    // HAVING conditions
    const havingConditions: string[] = [];

    if (status === 'Paid' || status === 'Pending' || status === 'Overdue') {
      havingConditions.push(`status = '${status}'`);
    }

    if (search) {
      const likeTerm = `%${search}%`;
      havingConditions.push(`(
        league_name LIKE '${likeTerm}' OR 
        season_name LIKE '${likeTerm}' OR 
        division_name LIKE '${likeTerm}'
      )`);
    }

    // Final query with HAVING
    let filteredQuery = baseQuery;
    if (havingConditions.length > 0) {
      filteredQuery += ` HAVING ` + havingConditions.join(' AND ');
    }

    // Total count (without LIMIT)
    const totalRows = await executeSecondaryQuery(`SELECT COUNT(*) as total FROM (${filteredQuery}) AS sub`, [userId]);
    const totalRecords = totalRows[0]?.total || 0;

    // Add LIMIT and OFFSET for pagination
    filteredQuery += ` LIMIT ${pageSize} OFFSET ${offset}`;

    // Final paginated data fetch
    const rows = await executeSecondaryQuery(filteredQuery, [userId]);

    const responseData = {
      data: rows,
      totalRecords,
      page,
      pageSize,
    };

    sendSuccess(res, responseData, 'Platform dues retrieved successfully');
  } catch (error) {
    logger.error('Error getting platform dues', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    sendError(res, 'Failed to retrieve platform dues', 500);
  }
};

export const getPlatformDuesDetail = async (
  req: Request & { userId?: string },
  res: Response
) => {
  try {
    const groupId = parseInt(req.params.groupId);
    if (!groupId) {
      return sendError(res, 'Group ID is required', 400);
    }

    const query = `
SELECT DISTINCT
  t.id AS team_id,
  t.team_name,

  -- Count all users in team using subquery
  (SELECT COUNT(*) 
   FROM team_members tm2 
   WHERE tm2.team_id = t.id) AS players,

  CASE 
    WHEN ot.team_id IS NOT NULL THEN 'Paid'
    ELSE 'Pending'
  END AS status,

   COALESCE(ROUND(
    CASE 
      WHEN ot.team_id IS NOT NULL THEN 
        o.team_charge_paid + (o.team_charge_paid * ((o.tax_paid + o.transaction_charge_paid) / 100))
      ELSE 
        pm.team_charge + (pm.team_charge * ((pm.tax + pm.platform_charge + pm.transaction_charges + pm.stripe_charges) / 100))
    END
  , 2), 0) AS amountDue

FROM game_groups_matches ggm
JOIN teams t ON FIND_IN_SET(t.id, ggm.group_teams)

LEFT JOIN team_members tm ON tm.team_id = t.id AND tm.role_id = 4
LEFT JOIN users u ON u.id = tm.user_id

LEFT JOIN order_teams ot ON ot.team_id = t.id 
  AND ot.group_id = ggm.id 
  AND ot.season_id = ggm.season_id

LEFT JOIN orders o ON o.id = ot.order_id

JOIN (
  SELECT *
  FROM price_master
  WHERE status_id = 1 AND enabled = 1
  ORDER BY created_at DESC
  LIMIT 1
) pm

WHERE ggm.id = ?
;



    `;

    const historyQuery = `SELECT
 
o.id,
o.stripe_transaction_id,
o.transaction_date,
o.card_details,
  JSON_ARRAYAGG(
    JSON_OBJECT(
     
      'team_id', t.id,
      'team_name', t.team_name
    )
  ) AS team_payments,

  ROUND(
    SUM(
      COALESCE(o.team_charge_paid + (o.team_charge_paid * ((o.tax_paid + o.transaction_charge_paid) / 100)), 0)
    ),
    2
  )AS total_paid_amount

FROM game_groups_matches ggm

JOIN teams t ON FIND_IN_SET(t.id, ggm.group_teams)

LEFT JOIN team_members tm ON tm.team_id = t.id AND tm.role_id = 4
LEFT JOIN users u ON u.id = tm.user_id

JOIN order_teams ot ON ot.team_id = t.id
  AND ot.group_id = ggm.id
  AND ot.season_id = ggm.season_id

JOIN orders o ON o.id = ot.order_id

WHERE ggm.id = ? AND o.payment_status_id =2
group by o.id,o.stripe_transaction_id

  `

    const rows = await executeSecondaryQuery(query, [groupId]);
    const history = await executeSecondaryQuery(historyQuery, [groupId]);

    sendSuccess(res, { rows, history }, 'Platform dues detail retrieved successfully');
  } catch (error) {
    logger.error('Error fetching platform dues detail', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to fetch platform dues detail', 500);
  }
};


export const getPlatformDuesSummary = async (
  req: Request & { userId?: string; team_connect_user_id?: string },
  res: Response
) => {
  try {
    const userId = req.team_connect_user_id;
    if (!userId) {
      return sendError(res, 'User ID is required', 400);
    }

    logger.info('Getting platform dues summary', { userId });

    // Get all dues for the user
    const query = `
     SELECT 
  s.id AS season_id,
  g.game_title AS league_name,
  s.season_name,
  ggm.group_name AS division_name,

  COUNT(DISTINCT t.id) AS teams_count,
  ROUND(
  ROUND(SUM(
    CASE 
      WHEN ot.team_id IS NOT NULL THEN 
        (
          (COALESCE(o.order_amount, 0)/100) +
          COALESCE(o.tax_paid, 0) +
          COALESCE(o.transaction_charge_paid, 0)
        ) / NULLIF(otc.total_teams_in_order, 0)
      ELSE 0
    END
  ), 2) 
  + 
  ROUND((COUNT(DISTINCT t.id) - COUNT(DISTINCT ot.team_id)) * (
    MAX(pm.team_charge) + 
    MAX(pm.tax) + 
    MAX(pm.platform_charge) + 
    MAX(pm.transaction_charges) + 
    MAX(pm.stripe_charges)
  ), 2)
, 2) AS total_amount,

  -- Amount paid = (order_amount + charges) / total_teams in that order
  ROUND(SUM(
    CASE 
      WHEN ot.team_id IS NOT NULL THEN 
        (
          (COALESCE(o.order_amount, 0)/100) +
          COALESCE(o.tax_paid, 0) +
          COALESCE(o.transaction_charge_paid, 0)
        ) / NULLIF(otc.total_teams_in_order, 0)
      ELSE 0
    END
  ),2) AS amount_paid,

  -- ✅ Safe fix using MAX() for price values
  ROUND((COUNT(DISTINCT t.id) - COUNT(DISTINCT ot.team_id)) * (
    MAX(pm.team_charge) + 
    MAX(pm.tax) + 
    MAX(pm.platform_charge) + 
    MAX(pm.transaction_charges) + 
    MAX(pm.stripe_charges)
  ),2) AS amount_pending,

  s.start_date AS due_date,

  CASE 
    WHEN (COUNT(DISTINCT t.id) - COUNT(DISTINCT ot.team_id)) = 0 THEN 'Paid'
    ELSE 'Pending'
  END AS status,

  MAX(ggm.created_at) AS created_at

FROM games g
JOIN seasons s ON s.game_id = g.id
JOIN game_groups_matches ggm ON ggm.season_id = s.id

LEFT JOIN teams t ON FIND_IN_SET(t.id, ggm.group_teams)
LEFT JOIN order_teams ot ON ot.team_id = t.id AND ot.group_id = ggm.id AND ot.season_id = s.id
LEFT JOIN orders o ON o.id = ot.order_id

-- Subquery: get total teams per order
LEFT JOIN (
  SELECT order_id, COUNT(*) AS total_teams_in_order
  FROM order_teams
  GROUP BY order_id
) otc ON otc.order_id = ot.order_id

-- Active price master
JOIN (
  SELECT *
  FROM price_master
  WHERE status_id = 1 AND enabled = 1
  ORDER BY created_at DESC
  LIMIT 1
) pm

WHERE g.user_id =?

GROUP BY 
  s.id, ggm.id, g.game_title, s.season_name, ggm.group_name, s.start_date;
    `;
    const dues = await executeSecondaryQuery(query, [userId]);

    // Calculate summary statistics
// Group dues by league_name
const leagueStatusMap: Record<string, string[]> = {};

dues.forEach((due: any) => {
  if (!leagueStatusMap[due.league_name]) {
    leagueStatusMap[due.league_name] = [];
  }
  leagueStatusMap[due.league_name].push(due.status);
});

// Classify leagues
let paidDues = 0;
let pendingDues = 0;
let overdueDues = 0;

for (const league in leagueStatusMap) {
  const statuses = leagueStatusMap[league];

  const allPaid = statuses.every(status => status === 'Paid');
  const anyPending = statuses.some(status => status === 'Pending');
  const anyOverdue = statuses.some(status => status === 'Overdue'); // if used

  if (allPaid) {
    paidDues++;
  } else if (anyPending) {
    pendingDues++;
  } else if (anyOverdue) {
    overdueDues++;
  }
}


// Total distinct leagues
const totalDues = new Set(dues.map((due: any) => due.league_name)).size;

    const totalTeams = dues.reduce((sum: number, due: any) => sum + (parseInt(due.teams_count) || 0), 0);

    // const dues = await executeSecondaryQuery(query, [userId]);
const totalAmountDue = dues
      .filter((due: any) => due.status !== 'Paid')
      .reduce((sum: number, due: any) => sum + (parseFloat(due.amount_pending) || 0), 0);

    const totalAmountPaid = dues
      .filter((due: any) => due.status === 'Paid')
      .reduce((sum: number, due: any) => sum + (parseFloat(due.amount_paid) || 0), 0);
    const summary = {
  totalDues,        // Total platform dues
  paidDues,         // Fully paid dues  
  pendingDues,      // Pending payment dues
  overdueDues,      // Overdue dues
  totalTeams,
 totalAmountDue: Math.round(totalAmountDue * 100) / 100, // Round to 2 decimal places
 totalAmountPaid: Math.round(totalAmountPaid * 100) / 100,
};

    logger.info('Successfully calculated platform dues summary', { userId, summary: { ...summary, recentPayments: 100} });

    sendSuccess(res, summary, 'Platform dues summary retrieved successfully');
  } catch (error) {
    logger.error('Error getting platform dues summary', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    sendError(res, 'Failed to retrieve platform dues summary', 500);
  }
};
