import { Request, Response } from 'express';
import {
  confirmPendingDeposit,
  confirmWithdrawalHold,
  releaseFailedHold
} from '../services/enhancedPendingBalanceService';
import { updateTransferStatus } from '../services/plaidTransferService';
import logger from '../utils/logger';
import { sendSuccess, sendError } from '../utils/response';
import { 
  verifyPlaidWebhookSignature, 
  extractWebhookSignature, 
  getRequestBody 
} from '../utils/webhookVerification';

/**
 * Handle Plaid webhook events for transfer status updates
 * This is the real-time way to get notified when transfers complete/fail
 * Based on Plaid Transfer documentation: https://plaid.com/docs/transfer/
 */
export const handlePlaidWebhook = async (req: Request, res: Response) => {
  try {
    const webhookEvent = req.body;
    
    // Log the complete webhook event for debugging
    logger.info('Received Plaid webhook', {
      webhookType: webhookEvent.webhook_type,
      webhookCode: webhookEvent.webhook_code,
      itemId: webhookEvent.item_id,
      transferId: webhookEvent.transfer_id,
      timestamp: webhookEvent.timestamp,
      environment: process.env.NODE_ENV
    });

    // Verify webhook signature (recommended for production)
    if (process.env.NODE_ENV === 'production') {
      const isValid = await verifyWebhookSignature(req);
      if (!isValid) {
        logger.error('Invalid webhook signature', { webhookEvent });
        return sendError(res, 'Invalid webhook signature', 401);
      }
    }

    // Handle transfer-related webhooks
    if (webhookEvent.webhook_type === 'TRANSFER') {
      await handleTransferWebhook(webhookEvent);
    } else {
      logger.info('Ignoring non-transfer webhook', {
        webhookType: webhookEvent.webhook_type,
        webhookCode: webhookEvent.webhook_code
      });
    }

    // Always respond with 200 to acknowledge receipt
    sendSuccess(res, { received: true }, 'Webhook processed successfully');

  } catch (error) {
    logger.error('Error processing Plaid webhook', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      webhook: req.body
    });
    
    // Still return 200 to prevent Plaid from retrying
    sendSuccess(res, { received: true, error: 'Processing failed but acknowledged' }, 'Webhook acknowledged');
  }
};

/**
 * Verify webhook signature for security
 */
async function verifyWebhookSignature(req: Request): Promise<boolean> {
  try {
    const webhookSecret = process.env.PLAID_WEBHOOK_SECRET;
    if (!webhookSecret) {
      logger.warn('PLAID_WEBHOOK_SECRET not configured, skipping signature verification');
      return true; // Allow in development
    }

    const signature = extractWebhookSignature(req);
    if (!signature) {
      logger.warn('No webhook signature found in request headers');
      return false;
    }

    const requestBody = getRequestBody(req);
    return verifyPlaidWebhookSignature(requestBody, signature, webhookSecret);
  } catch (error) {
    logger.error('Error verifying webhook signature', { error });
    return false;
  }
}

/**
 * Handle transfer-specific webhook events
 * Based on Plaid Transfer webhook documentation
 */
async function handleTransferWebhook(webhookEvent: any) {
  const { webhook_code, transfer_id, transfer_status, failure_reason, return_reason } = webhookEvent;
  
  if (!transfer_id) {
    logger.warn('Transfer webhook received without transfer_id', { webhookEvent });
    return;
  }

  logger.info('Processing transfer webhook', {
    webhookCode: webhook_code,
    transferId: transfer_id,
    transferStatus: transfer_status,
    failureReason: failure_reason,
    returnReason: return_reason
  });

  try {
    switch (webhook_code) {
      case 'TRANSFER_EVENTS_UPDATE':
        // Transfer status has changed - use the status from the webhook
        await handleTransferStatusUpdate(transfer_id, transfer_status, failure_reason);
        break;
        
      case 'TRANSFER_POSTED':
        // Transfer has been posted (completed)
        await handleTransferPosted(transfer_id);
        break;
        
      case 'TRANSFER_SETTLED':
        // Transfer has been settled (final completion)
        await handleTransferSettled(transfer_id);
        break;
        
      case 'TRANSFER_FAILED':
        // Transfer has failed
        await handleTransferFailed(transfer_id, failure_reason);
        break;
        
      case 'TRANSFER_CANCELLED':
        // Transfer was cancelled
        await handleTransferCancelled(transfer_id);
        break;
        
      case 'TRANSFER_RETURNED':
        // Transfer was returned
        await handleTransferReturned(transfer_id, return_reason);
        break;
        
      case 'TRANSFER_PENDING_APPROVAL':
        // Transfer is pending approval
        await handleTransferPendingApproval(transfer_id);
        break;
        
      case 'TRANSFER_APPROVED':
        // Transfer has been approved
        await handleTransferApproved(transfer_id);
        break;
        
      case 'TRANSFER_REJECTED':
        // Transfer has been rejected
        await handleTransferRejected(transfer_id, failure_reason);
        break;
        
      default:
        logger.info('Unhandled transfer webhook code', {
          webhookCode: webhook_code,
          transferId: transfer_id,
          transferStatus: transfer_status
        });
    }
  } catch (error) {
    logger.error('Error handling transfer webhook', {
      webhookCode: webhook_code,
      transferId: transfer_id,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}

/**
 * Handle general transfer status updates
 * Now uses the status directly from the webhook instead of making an API call
 */
async function handleTransferStatusUpdate(transferId: string, transferStatus: string, failureReason?: any) {
  try {
    // Update the database status directly from webhook
    await updateTransferStatus(transferId, transferStatus, failureReason?.description);
    
    // Process pending holds based on status
    if (transferStatus === 'posted' || transferStatus === 'settled') {
      await handleTransferSuccess(transferId, transferStatus);
    } else if (transferStatus === 'failed' || transferStatus === 'cancelled' || transferStatus === 'returned' || transferStatus === 'rejected') {
      await handleTransferFailure(transferId, failureReason?.description || 'Transfer failed');
    } else if (transferStatus === 'pending_approval') {
      await handleTransferPendingApproval(transferId);
    } else if (transferStatus === 'approved') {
      await handleTransferApproved(transferId);
    }
    
    logger.info('Transfer status updated via webhook', {
      transferId,
      status: transferStatus,
      failureReason: failureReason?.description
    });
  } catch (error) {
    logger.error('Error handling transfer status update', {
      transferId,
      transferStatus,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}

/**
 * Handle successful transfer completion
 */
async function handleTransferSuccess(transferId: string, status: string) {
  try {
    logger.info('Processing successful transfer', { transferId, status });
    
    // Try to confirm withdrawal hold (for staff payments and withdrawals)
    const withdrawalResult = await confirmWithdrawalHold(transferId, status);

    if (withdrawalResult.success) {
      logger.info('Withdrawal hold confirmed via webhook', {
        transferId,
        status,
        userId: withdrawalResult.userId,
        amount: withdrawalResult.amount
      });
      return;
    }

    // Try to confirm deposit (for add money)
    const depositResult = await confirmPendingDeposit(transferId, status);

    if (depositResult.success) {
      logger.info('Deposit confirmed via webhook', {
        transferId,
        status,
        userId: depositResult.userId,
        amount: depositResult.amount
      });
      return;
    }

    // If neither worked, just log it
    logger.warn('Transfer completed but no pending hold found', {
      transferId,
      status
    });

  } catch (error) {
    logger.error('Error handling transfer success', {
      transferId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}

/**
 * Handle transfer failure
 */
async function handleTransferFailure(transferId: string, failureReason: string) {
  try {
    logger.info('Processing failed transfer', { transferId, failureReason });
    
    // Release any failed holds
    const releaseResult = await releaseFailedHold(transferId, failureReason);

    if (releaseResult.success) {
      logger.info('Failed hold released via webhook', {
        transferId,
        failureReason,
        userId: releaseResult.userId,
        amount: releaseResult.amount
      });
    } else {
      logger.warn('Transfer failed but no pending hold found', {
        transferId,
        failureReason
      });
    }

  } catch (error) {
    logger.error('Error handling transfer failure', {
      transferId,
      failureReason,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}

/**
 * Handle transfer posted event
 */
async function handleTransferPosted(transferId: string) {
  await handleTransferSuccess(transferId, 'posted');
}

/**
 * Handle transfer settled event
 */
async function handleTransferSettled(transferId: string) {
  await handleTransferSuccess(transferId, 'settled');
}

/**
 * Handle transfer failed event
 */
async function handleTransferFailed(transferId: string, failureReason: any) {
  const reason = failureReason?.description || 'Transfer failed';
  await handleTransferFailure(transferId, reason);
}

/**
 * Handle transfer cancelled event
 */
async function handleTransferCancelled(transferId: string) {
  await handleTransferFailure(transferId, 'Transfer cancelled');
}

/**
 * Handle transfer returned event
 */
async function handleTransferReturned(transferId: string, returnReason: any) {
  const reason = returnReason?.description || 'Transfer returned';
  await handleTransferFailure(transferId, `Transfer returned: ${reason}`);
}

/**
 * Handle transfer pending approval event
 */
async function handleTransferPendingApproval(transferId: string) {
  try {
    logger.info('Transfer pending approval', { transferId });
    
    // Update transfer status to pending_approval
    await updateTransferStatus(transferId, 'pending_approval');
    
  } catch (error) {
    logger.error('Error handling transfer pending approval', {
      transferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Handle transfer approved event
 */
async function handleTransferApproved(transferId: string) {
  try {
    logger.info('Transfer approved', { transferId });
    
    // Update transfer status to approved
    await updateTransferStatus(transferId, 'approved');
    
  } catch (error) {
    logger.error('Error handling transfer approved', {
      transferId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Handle transfer rejected event
 */
async function handleTransferRejected(transferId: string, failureReason: any) {
  const reason = failureReason?.description || 'Transfer rejected';
  await handleTransferFailure(transferId, `Transfer rejected: ${reason}`);
}