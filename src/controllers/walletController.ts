import { Request, Response } from 'express';
import {
  checkPinSetupRequired,
  getUserMasterWallet,
  createMasterWallet,
  setWalletMasterPin,
  verifyWalletMasterPin,
  updateWalletBalance,
  getWalletBalance,
  getWalletStats,
  addMoneyFromBank,
  withdrawMoneyToBank,
  getWalletTransactions,
  getPrimaryBankAccount,
  transferWalletToWallet,
  searchTransferRecipients
} from '../services/walletService';
import { executeQuery, executeQuerySingle } from '../utils/database';

import { getPendingTransfers } from '../services/plaidTransferService';
import { sendSuccess, sendError, sendUnauthorized, sendUserSuccess, sendUserError } from '../utils/response';
import { formatAmount, getBalanceMessage } from '../utils/userMessages';
import { hashPin, comparePin } from '../models/user';
import {
  createWalletLoadingSkeleton,
  sendLoadingResponse,
  LOADING_MESSAGES,
  transformToSkeletonStructure
} from '../utils/loadingStates';
import {
  generateAndSaveOTP,
  generateAndSendOTP,
  verifyOTPCode,
  markOTPAsUsed,
  invalidateUserOTPs,
  OTP_CONFIG
} from '../services/otpService';
import { getPlaidLedgerBalance, fundPlaidLedger } from '../utils/plaidLedgerUtils';
import { plaidClient } from '../config/plaidConfig';

import logger from '../utils/logger';

/**
 * Get user's wallet information
 */
export const getWalletInfo = async (req: Request & { userId?: string; parent_user_id?: string; sessionToken?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const wallet = await getUserMasterWallet(parseInt(userId));
    const requiresPinSetup = await checkPinSetupRequired(parseInt(userId));
    const stats = await getWalletStats(parseInt(userId));

    if (!wallet) {
      return sendSuccess(res, {
        hasWallet: false,
        requiresPinSetup: true,
        stats
      }, 'No wallet found for user');
    }

    sendSuccess(res, {
      hasWallet: true,
      requiresPinSetup,
      wallet: {
        id: wallet.id,
        walletUniqueId: wallet.wallet_unique_id,
        name: wallet.name,
        username: wallet.username,
        balance: wallet.balance,
        statusId: wallet.status_id,
        createdAt: wallet.created_at,
        lastUpdated: wallet.last_updated
      },
      stats
    }, 'Wallet information retrieved successfully');

  } catch (error) {
    logger.error('Error getting wallet info', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve wallet information', 500);
  }
};

/**
 * Create a new master wallet for user with PIN
 */
export const createWallet = async (req: Request & { userId?: string; parent_user_id?: string; sessionToken?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { pin } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!pin || typeof pin !== 'string') {
      return sendError(res, 'Master PIN is required', 400);
    }

    // Validate PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(pin)) {
      return sendError(res, 'PIN must be 4-6 digits', 400);
    }

    // Check if user already has a wallet
    const existingWallet = await getUserMasterWallet(parseInt(userId));
    if (existingWallet) {
      return sendError(res, 'User already has a master wallet', 409);
    }

    // Hash the PIN
    const hashedPin = await hashPin(pin);

    // Create wallet with PIN
    const wallet = await createMasterWallet(parseInt(userId), hashedPin);

    if (!wallet) {
      return sendUserError(res, 'WALLET', 'CREATED', 500);
    }

    sendUserSuccess(res, 'WALLET', 'CREATED', {
      wallet: {
        id: wallet.id,
        walletUniqueId: wallet.wallet_unique_id,
        name: wallet.name,
        username: wallet.username,
        balance: wallet.balance,
        formattedBalance: formatAmount(wallet.balance),
        balanceMessage: getBalanceMessage(wallet.balance),
        statusId: wallet.status_id,
        createdAt: wallet.created_at,
        lastUpdated: wallet.last_updated
      },
      requiresPinSetup: false // PIN is already set during creation
    });

  } catch (error) {
    logger.error('Error creating wallet', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to create wallet', 500);
  }
};

/**
 * Send OTP for PIN change verification
 */
export const sendPinChangeOTP = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get user details
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet or PIN found', 404);
    }

    // Invalidate all previous unused transaction OTPs
    await invalidateUserOTPs(user.id, 'transaction');

    // Generate, save and send new OTP for transaction (PIN change)
    const { otpCode, otpId, emailSent } = await generateAndSendOTP(user.id, user.email, 'transaction');

    logger.info('PIN change OTP sent', {
      userId: user.id,
      email: user.email,
      otpId,
      emailSent,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

    sendUserSuccess(res, 'PIN', 'CHANGE_OTP_SENT', {
      message: 'Security code sent for PIN change',
      email: user.email,
      emailSent,
      otp: process.env.NODE_ENV === 'development' ? otpCode : undefined,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

  } catch (error) {
    logger.error('Error sending PIN change OTP', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to send OTP', 500);
  }
};

/**
 * Send OTP for PIN reset (forgot PIN)
 */
export const sendPinResetOTP = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Get user details
    const user = await executeQuerySingle(
      'SELECT * FROM tbl_users WHERE id = ?',
      [userId]
    );

    if (!user) {
      return sendError(res, 'User not found', 404);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet) {
      return sendError(res, 'No wallet found', 404);
    }

    // Invalidate all previous unused PIN reset OTPs (using 'password_reset' type)
    await invalidateUserOTPs(user.id, 'password_reset');

    // Generate, save and send new OTP for PIN reset (using 'password_reset' type as it's similar)
    const { otpCode, otpId, emailSent } = await generateAndSendOTP(user.id, user.email, 'password_reset');

    logger.info('PIN reset OTP sent', {
      userId: user.id,
      email: user.email,
      otpId,
      emailSent,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

    sendUserSuccess(res, 'PIN', 'RESET_OTP_SENT', {
      message: 'Security code sent for PIN reset',
      email: user.email,
      emailSent,
      otp: process.env.NODE_ENV === 'development' ? otpCode : undefined,
      expiresInMinutes: OTP_CONFIG.EXPIRY_MINUTES
    });

  } catch (error) {
    logger.error('Error sending PIN reset OTP', { error, userId: req.userId });
    sendError(res, 'Failed to send PIN reset code', 500);
  }
};

/**
 * Reset wallet PIN with OTP verification (forgot PIN)
 */
export const resetWalletPin = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { newPin, otpCode } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!newPin || !otpCode) {
      return sendError(res, 'New PIN and OTP code are required', 400);
    }

    // Validate new PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(newPin)) {
      return sendError(res, 'New PIN must be 4-6 digits', 400);
    }

    // Validate OTP format
    if (typeof otpCode !== 'string' || otpCode.length !== OTP_CONFIG.LENGTH) {
      return sendError(res, `OTP must be ${OTP_CONFIG.LENGTH} digits`, 400);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet) {
      return sendError(res, 'No wallet found', 404);
    }

    // Verify OTP for PIN reset (using 'password_reset' type as it's similar)
    const otpVerification = await verifyOTPCode(parseInt(userId), otpCode, 'password_reset');
    if (!otpVerification.isValid) {
      logger.warn('OTP verification failed during PIN reset', {
        userId,
        otpCode,
        error: otpVerification.error
      });
      return sendError(res, otpVerification.error || 'Invalid OTP code', 400);
    }

    // Mark OTP as used
    if (otpVerification.otpRecord) {
      await markOTPAsUsed(otpVerification.otpRecord.id);
    }

    // Hash the new PIN
    const hashedNewPin = await hashPin(newPin);

    // Update the PIN
    const success = await setWalletMasterPin(parseInt(userId), hashedNewPin);

    if (!success) {
      return sendError(res, 'Failed to reset wallet PIN', 500);
    }

    logger.info('Wallet PIN reset successfully', { userId });

    sendUserSuccess(res, 'PIN', 'RESET_SUCCESS', {
      message: 'PIN reset successfully'
    });

  } catch (error) {
    logger.error('Error resetting wallet PIN', { error, userId: req.userId });
    sendError(res, 'Failed to reset wallet PIN', 500);
  }
};

/**
 * Change wallet master PIN (for existing wallets) - now requires OTP verification
 */
export const changeWalletPin = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { currentPin, newPin, otpCode } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!currentPin || !newPin || !otpCode) {
      return sendError(res, 'Current PIN, new PIN, and OTP code are required', 400);
    }

    // Validate new PIN format (should be 4-6 digits)
    if (!/^\d{4,6}$/.test(newPin)) {
      return sendError(res, 'New PIN must be 4-6 digits', 400);
    }

    // Validate OTP format
    if (typeof otpCode !== 'string' || otpCode.length !== OTP_CONFIG.LENGTH) {
      return sendError(res, `OTP must be ${OTP_CONFIG.LENGTH} digits`, 400);
    }

    // Check if user has a wallet
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet or PIN found', 404);
    }

    // Verify current PIN
    const isCurrentPinValid = await comparePin(currentPin, wallet.wallet_master_pin);
    if (!isCurrentPinValid) {
      logger.warn('Invalid current PIN attempt during PIN change', { userId });
      return sendError(res, 'Current PIN is incorrect', 400);
    }

    // Verify OTP
    const otpVerification = await verifyOTPCode(parseInt(userId), otpCode, 'transaction');
    if (!otpVerification.isValid) {
      logger.warn('OTP verification failed during PIN change', {
        userId,
        otpCode,
        error: otpVerification.error
      });
      return sendError(res, otpVerification.error || 'Invalid OTP code', 400);
    }

    // Mark OTP as used
    if (otpVerification.otpRecord) {
      await markOTPAsUsed(otpVerification.otpRecord.id);
    }

    // Hash the new PIN
    const hashedNewPin = await hashPin(newPin);

    // Update the PIN
    const success = await setWalletMasterPin(parseInt(userId), hashedNewPin);

    if (!success) {
      return sendError(res, 'Failed to change wallet PIN', 500);
    }

    logger.info('Wallet PIN changed successfully with OTP verification', { userId });

    sendSuccess(res, {
      message: 'PIN changed successfully'
    }, 'Wallet PIN changed successfully');

  } catch (error) {
    logger.error('Error changing wallet PIN', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to change wallet PIN', 500);
  }
};

/**
 * Verify wallet master PIN
 */
export const verifyWalletPin = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { pin } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!pin || typeof pin !== 'string') {
      return sendError(res, 'PIN is required', 400);
    }

    // Get wallet with PIN
    const wallet = await getUserMasterWallet(parseInt(userId));
    if (!wallet || !wallet.wallet_master_pin) {
      return sendError(res, 'No wallet PIN found', 404);
    }

    // Verify PIN
    const isValid = await comparePin(pin, wallet.wallet_master_pin);

    if (!isValid) {
      logger.warn('Invalid wallet PIN attempt', { userId });
      return sendError(res, 'Invalid PIN', 400);
    }

    sendSuccess(res, {
      verified: true
    }, 'PIN verified successfully');

  } catch (error) {
    logger.error('Error verifying wallet PIN', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to verify wallet PIN', 500);
  }
};

/**
 * Get wallet balance
 */
export const getBalance = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const balance = await getWalletBalance(parseInt(userId));

    if (balance === null) {
      return sendError(res, 'No wallet found', 404);
    }

    sendSuccess(res, {
      balance
    }, 'Balance retrieved successfully');

  } catch (error) {
    logger.error('Error getting wallet balance', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve balance', 500);
  }
};

/**
 * Add money to wallet from primary bank account with PIN verification
 */
export const addMoney = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { amount, pin, paymentMethod, bankAccountId } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return sendUserError(res, 'MONEY', 'INVALID_AMOUNT', 400);
    }

    if (!pin || typeof pin !== 'string') {
      return sendUserError(res, 'PIN', 'VERIFIED', 400);
    }

    if (amount < 10) {
      return sendError(res, 'Minimum amount is $10.00 to add money to your wallet', 400);
    }

    if (amount > 10000) {
      return sendError(res, 'Maximum amount is $10,000.00 per transaction for your security', 400);
    }

    // Use the new service function that handles PIN verification and bank transfer
    const result = await addMoneyFromBank(parseInt(userId), amount, pin, bankAccountId);

    if (result.success) {
      const newBalance = result.newBalance || 0;
      sendUserSuccess(res, 'MONEY', 'ADDED', {
        success: true,
        newBalance,
        formattedNewBalance: formatAmount(newBalance),
        amount,
        formattedAmount: formatAmount(amount),
        transactionId: result.transactionId,
        message: result.message,
        balanceMessage: getBalanceMessage(newBalance)
      });
    } else {
      sendUserError(res, 'MONEY', 'ADDED', 400, result.message);
    }

  } catch (error) {
    logger.error('Error adding money to wallet', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to add money to wallet', 500);
  }
};

/**
 * Withdraw money from wallet to primary bank account with PIN verification
 */
export const withdrawMoney = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { amount, pin, bankAccountId, paymentMethod } = req.body;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!amount || typeof amount !== 'number' || amount <= 0) {
      return sendError(res, 'Valid amount is required', 400);
    }

    if (!pin || typeof pin !== 'string') {
      return sendError(res, 'Wallet PIN is required', 400);
    }

    if (amount < 10) {
      return sendError(res, 'Minimum withdrawal amount is $10', 400);
    }

    if (amount > 5000) {
      return sendError(res, 'Maximum withdrawal amount is $5,000', 400);
    }

    // Use the new service function that handles PIN verification and bank transfer
    const result = await withdrawMoneyToBank(parseInt(userId), amount, pin, bankAccountId, paymentMethod);

    if (result.success) {
      sendSuccess(res, {
        success: true,
        newBalance: result.newBalance,
        amount,
        transactionId: result.transactionId,
        estimatedArrival: '1-3 business days',
        message: result.message
      }, 'Withdrawal initiated successfully');
    } else {
      sendError(res, result.message || 'Failed to withdraw money', 400);
    }

  } catch (error) {
    logger.error('Error withdrawing money from wallet', {
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.name : undefined,
      userId: req.userId,
      requestBody: req.body,
      sqlState: (error as any)?.sqlState,
      sqlMessage: (error as any)?.sqlMessage,
      errno: (error as any)?.errno,
      code: (error as any)?.code
    });
    sendError(res, 'Failed to withdraw money from wallet', 500);
  }
};



/**
 * Get wallet transaction history
 */
export const getTransactionHistory = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { limit = 50, offset = 0 } = req.query;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const transactions = await getWalletTransactions(
      parseInt(userId),
      parseInt(limit as string),
      parseInt(offset as string)
    );

    // Format transactions for frontend
    const formattedTransactions = transactions.map(transaction => ({
      id: transaction.id,
      type: transaction.amount > 0 ? 'credit' : 'debit',
      amount: Math.abs(transaction.amount),
      description: transaction.description,
      provider: transaction.payment_provider,
      reference: transaction.reference_id,
      status: parseInt(transaction.status_id.toString()) === 1 ? 'completed' : 'pending',
      date: transaction.created_at,
      metadata: transaction.meta_data
    }));

    sendSuccess(res, {
      transactions: formattedTransactions,
      total: formattedTransactions.length,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string)
    }, 'Transaction history retrieved successfully');

  } catch (error) {
    logger.error('Error getting transaction history', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve transaction history', 500);
  }
};

/**
 * Get enhanced wallet information including bank account details
 */
export const getEnhancedWalletInfo = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const wallet = await getUserMasterWallet(parseInt(userId));
    const requiresPinSetup = await checkPinSetupRequired(parseInt(userId));
    const stats = await getWalletStats(parseInt(userId));
    const primaryBank = await getPrimaryBankAccount(parseInt(userId));

    if (!wallet) {
      return sendSuccess(res, {
        hasWallet: false,
        requiresPinSetup: true,
        stats,
        primaryBank: null
      }, 'No wallet found for user');
    }

    sendSuccess(res, {
      hasWallet: true,
      requiresPinSetup,
      wallet: {
        id: wallet.id,
        walletUniqueId: wallet.wallet_unique_id,
        name: wallet.name,
        username: wallet.username,
        balance: wallet.balance,
        statusId: wallet.status_id,
        createdAt: wallet.created_at,
        lastUpdated: wallet.last_updated
      },
      primaryBank: primaryBank ? {
        id: primaryBank.id,
        bankName: primaryBank.bank_name,
        accountMask: primaryBank.account_mask,
        accountType: primaryBank.account_type,
        isPrimary: primaryBank.is_primary
      } : null,
      stats
    }, 'Enhanced wallet information retrieved successfully');

  } catch (error) {
    logger.error('Error getting enhanced wallet info', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve wallet information', 500);
  }
};

/**
 * Check PIN setup status
 */
export const checkPinStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const requiresPinSetup = await checkPinSetupRequired(parseInt(userId));

    sendSuccess(res, {
      requiresPinSetup
    }, 'PIN status checked successfully');

  } catch (error) {
    logger.error('Error checking PIN status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to check PIN status', 500);
  }
};

/**
 * Get dashboard data (wallet stats + recent transactions)
 */
export const getDashboardData = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    console.log(userId, 'userrrrid')
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Check if client wants loading state first
    const includeLoading = req.query.loading === 'true';
    if (includeLoading) {
      const skeleton = createWalletLoadingSkeleton();
      skeleton.loading.loadingMessage = LOADING_MESSAGES.WALLET.OVERVIEW;
      return sendLoadingResponse(res, skeleton, LOADING_MESSAGES.WALLET.OVERVIEW);
    }

    // Get wallet info
    const wallet = await getUserMasterWallet(parseInt(userId));
    const requiresPinSetup = await checkPinSetupRequired(parseInt(userId));
    const stats = await getWalletStats(parseInt(userId));
    const primaryBank = await getPrimaryBankAccount(parseInt(userId));

    // Get recent transactions (last 10)
    const transactions = await getWalletTransactions(parseInt(userId), 10, 0);

    // Prepare dashboard data
    const dashboardData = {
      wallet: wallet ? {
        id: wallet.id,
        walletUniqueId: wallet.wallet_unique_id,
        name: wallet.name,
        username: wallet.username,
        balance: wallet.balance,
        statusId: wallet.status_id,
        createdAt: wallet.created_at,
        lastUpdated: wallet.last_updated
      } : null,
      hasWallet: !!wallet,
      requiresPinSetup,
      stats,
      primaryBank,
      recentTransactions: transactions
    };

    // Add user-friendly formatting to dashboard data
    const enhancedDashboardData = {
      ...dashboardData,
      wallet: dashboardData.wallet ? {
        ...dashboardData.wallet,
        formattedBalance: formatAmount(dashboardData.wallet.balance),
        balanceMessage: getBalanceMessage(dashboardData.wallet.balance)
      } : null,
      // Add structured summary cards data for frontend
      summaryCards: [
        {
          value: wallet ? `$${parseFloat(wallet.balance.toString()).toFixed(2)}` : '$0.00',
          trend: {
            direction: 'neutral' as const,
            value: '0%'
          }
        },
        {
          value: '$0.00', // Pending balance - would need calculation
          trend: {
            direction: 'neutral' as const,
            value: '0%'
          }
        },
        {
          value: stats?.totalTransactions || 0,
          trend: {
            direction: 'neutral' as const,
            value: '0%'
          }
        },
        {
          value: stats?.thisMonthTotal ? `$${parseFloat(stats.thisMonthTotal.toString()).toFixed(2)}` : '$0.00',
          trend: {
            direction: 'neutral' as const,
            value: '0%'
          }
        }
      ],
      recentItems: transactions?.slice(0, 5) || []
    };

    sendUserSuccess(res, 'DASHBOARD', 'LOADED', enhancedDashboardData);

  } catch (error) {
    logger.error('Error getting dashboard data', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve dashboard data', 500);
  }
};

/**
 * Get pending transfers for user
 */
export const getPendingTransfersController = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const pendingTransfers = await getPendingTransfers(parseInt(userId));

    sendSuccess(res, {
      pendingTransfers,
      count: pendingTransfers.length
    }, 'Pending transfers retrieved successfully');

  } catch (error) {
    logger.error('Error getting pending transfers', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to retrieve pending transfers', 500);
  }
};

/**
 * Transfer money from one wallet to another
 */
export const checkUserWallet = async (req: Request, res: Response) => {
  try {
    const { userId } = req.query;

    if (!userId) {
      return sendError(res, 'User ID is required', 400);
    }

    const wallet = await getUserMasterWallet(parseInt(userId as string));

    sendSuccess(res, {
      userId: parseInt(userId as string),
      hasWallet: !!wallet,
      walletId: wallet?.wallet_unique_id,
      balance: wallet?.balance
    }, 'Wallet check completed');
  } catch (error) {
    logger.error('Error checking user wallet', { userId: req.query.userId, error });
    sendError(res, 'Failed to check user wallet', 500);
  }
};

/**
 * Get withdrawal status and progress with comprehensive failure handling
 */
export const getWithdrawalStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    const { transactionId } = req.params;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!transactionId) {
      return sendError(res, 'Transaction ID is required', 400);
    }

    // Verify transaction belongs to user
    const transaction = await executeQuerySingle(
      'SELECT * FROM tbl_wallet_transactions WHERE id = ? AND user_id = ?',
      [transactionId, parseInt(userId)]
    );

    if (!transaction) {
      return sendError(res, 'Transaction not found', 404);
    }

    // Get withdrawal progress steps
    const progressSteps = await executeQuery(`
      SELECT
        wp.step_key,
        wp.status,
        wp.completed_at,
        wp.notes,
        wp.created_at,
        ws.title,
        ws.description,
        ws.step_order
      FROM tbl_withdrawal_progress wp
      JOIN tbl_withdrawal_steps ws ON wp.step_key = ws.step_key
      WHERE wp.transaction_id = ?
      ORDER BY ws.step_order ASC
    `, [transactionId]);

    // Get all possible steps for this withdrawal type
    const allSteps = await executeQuery(`
      SELECT step_key, title, description, step_order
      FROM tbl_withdrawal_steps
      WHERE is_active = 1
      ORDER BY step_order ASC
    `);

    // Parse metadata
    const metaData = typeof transaction.meta_data === 'string'
      ? JSON.parse(transaction.meta_data)
      : transaction.meta_data;

    // Determine current status
    const completedSteps = progressSteps.filter(step => step.status === 'completed');
    const failedSteps = progressSteps.filter(step => step.status === 'failed');
    const currentStep = progressSteps.find(step => step.status === 'pending') ||
      completedSteps[completedSteps.length - 1];

    let overallStatus = 'pending';
    if (failedSteps.length > 0) {
      overallStatus = 'failed';
    } else if (progressSteps.some(step => step.step_key === 'completed' && step.status === 'completed')) {
      overallStatus = 'completed';
    } else if (progressSteps.some(step => step.step_key === 'reversed' && step.status === 'completed')) {
      overallStatus = 'reversed';
    }

    sendSuccess(res, {
      transaction: {
        id: transaction.id,
        amount: Math.abs(parseFloat(transaction.amount.toString())),
        description: transaction.description,
        created_at: transaction.created_at,
        paymentMethod: metaData?.paymentMethodName || 'ACH Standard',
        bankName: metaData?.bankName || 'Bank Account',
        estimatedArrival: metaData?.estimatedArrival || '1-3 business days',
        processingFee: metaData?.processingFee || 0
      },
      status: {
        overall: overallStatus,
        currentStep: currentStep?.step_key || 'initiated',
        currentStepTitle: currentStep?.title || 'Processing',
        currentStepDescription: currentStep?.description || 'Your withdrawal is being processed',
        lastUpdated: currentStep?.completed_at || currentStep?.created_at
      },
      progress: {
        completed: completedSteps.length,
        total: progressSteps.length,
        percentage: progressSteps.length > 0 ? Math.round((completedSteps.length / progressSteps.length) * 100) : 0
      },
      steps: progressSteps.map(step => ({
        key: step.step_key,
        title: step.title,
        description: step.description,
        status: step.status,
        completedAt: step.completed_at,
        notes: step.notes,
        order: step.step_order
      })),
      timeline: progressSteps
        .filter(step => step.status === 'completed' || step.status === 'failed')
        .sort((a, b) => new Date(a.completed_at || a.created_at).getTime() - new Date(b.completed_at || b.created_at).getTime())
        .map(step => ({
          title: step.title,
          description: step.notes || step.description,
          timestamp: step.completed_at || step.created_at,
          status: step.status
        }))
    }, 'Withdrawal status retrieved successfully');

  } catch (error) {
    logger.error('Error getting withdrawal status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId,
      transactionId: req.params.transactionId
    });
    sendError(res, 'Failed to get withdrawal status', 500);
  }
};

/**
 * Get Plaid Ledger balance and status
 */
export const getPlaidLedgerStatus = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Only allow admin users to check ledger status
    // You can add admin check here if needed

    const ledgerStatus = await getPlaidLedgerBalance();

    if (!ledgerStatus.success) {
      return sendError(res, ledgerStatus.message || 'Failed to get ledger balance', 500);
    }

    const availableBalance = parseFloat(ledgerStatus.balance!.available);
    const pendingBalance = parseFloat(ledgerStatus.balance!.pending);
    const totalBalance = availableBalance + pendingBalance;

    sendSuccess(res, {
      balance: {
        available: availableBalance,
        pending: pendingBalance,
        total: totalBalance,
        formattedAvailable: formatAmount(availableBalance),
        formattedPending: formatAmount(pendingBalance),
        formattedTotal: formatAmount(totalBalance)
      },
      status: {
        canProcessTransfers: availableBalance >= 10,
        needsSettlement: pendingBalance > 0,
        message: pendingBalance > 0 
          ? `$${pendingBalance.toFixed(2)} pending settlement`
          : 'All funds are available'
      }
    }, 'Plaid Ledger status retrieved successfully');

  } catch (error) {
    logger.error('Error getting Plaid Ledger status', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to get Plaid Ledger status', 500);
  }
};

/**
 * Settle pending funds in Plaid Ledger (sandbox only)
 */
export const settlePlaidLedger = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;

    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    // Only allow admin users to settle ledger
    // You can add admin check here if needed

    // Check current balance before settlement
    const beforeStatus = await getPlaidLedgerBalance();
    if (!beforeStatus.success) {
      return sendError(res, 'Failed to check current ledger balance', 500);
    }

    const beforeAvailable = parseFloat(beforeStatus.balance!.available);
    const beforePending = parseFloat(beforeStatus.balance!.pending);

    if (beforePending <= 0) {
      return sendSuccess(res, {
        message: 'No pending funds to settle',
        balance: {
          available: beforeAvailable,
          pending: beforePending,
          settled: 0
        }
      }, 'No settlement needed');
    }

    // Attempt to trigger settlement using sandbox webhook
    try {
      await plaidClient.sandboxTransferFireWebhook({
        webhook_code: 'TRANSFER_EVENTS_UPDATE'
      });

      logger.info('Settlement webhook fired', { userId, beforePending });

      // Wait for settlement to process
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (webhookError) {
      logger.warn('Webhook settlement failed, trying alternative method', { 
        error: webhookError instanceof Error ? webhookError.message : 'Unknown error' 
      });

      // Alternative: Create a small deposit to trigger settlement
      try {
        await fundPlaidLedger(1.00);
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (altError) {
        logger.warn('Alternative settlement method failed', { 
          error: altError instanceof Error ? altError.message : 'Unknown error' 
        });
      }
    }

    // Check balance after settlement attempt
    const afterStatus = await getPlaidLedgerBalance();
    if (!afterStatus.success) {
      return sendError(res, 'Failed to check ledger balance after settlement', 500);
    }

    const afterAvailable = parseFloat(afterStatus.balance!.available);
    const afterPending = parseFloat(afterStatus.balance!.pending);
    const settledAmount = beforePending - afterPending;

    logger.info('Plaid Ledger settlement completed', {
      userId,
      beforeAvailable,
      beforePending,
      afterAvailable,
      afterPending,
      settledAmount
    });

    sendSuccess(res, {
      message: settledAmount > 0 
        ? `Successfully settled $${settledAmount.toFixed(2)}`
        : 'Settlement may still be processing',
      settlement: {
        settledAmount,
        formattedSettledAmount: formatAmount(settledAmount)
      },
      balance: {
        before: {
          available: beforeAvailable,
          pending: beforePending,
          formattedAvailable: formatAmount(beforeAvailable),
          formattedPending: formatAmount(beforePending)
        },
        after: {
          available: afterAvailable,
          pending: afterPending,
          formattedAvailable: formatAmount(afterAvailable),
          formattedPending: formatAmount(afterPending)
        }
      },
      status: {
        canProcessTransfers: afterAvailable >= 10,
        needsSettlement: afterPending > 0,
        message: afterPending > 0 
          ? `$${afterPending.toFixed(2)} still pending settlement`
          : 'All funds are now available'
      }
    }, 'Plaid Ledger settlement completed');

  } catch (error) {
    logger.error('Error settling Plaid Ledger', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to settle Plaid Ledger', 500);
  }
};

export const transferToWallet = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { recipientUserId, amount, pin } = req.body;

    logger.info('Transfer to wallet request received', {
      fromUserId: userId,
      recipientUserId,
      amount,
      pinLength: pin?.length,
      requestBody: req.body
    });

    // Validate input
    if (!recipientUserId || !amount || !pin) {
      return sendError(res, 'Recipient, amount, and PIN are required', 400);
    }

    if (typeof amount !== 'number' || amount <= 0) {
      return sendError(res, 'Invalid amount', 400);
    }

    if (typeof pin !== 'string' || pin.length < 4 || pin.length > 6) {
      return sendError(res, 'PIN must be 4-6 digits', 400);
    }

    // Perform the transfer
    const result = await transferWalletToWallet(
      parseInt(userId),
      parseInt(recipientUserId),
      amount,
      pin
    );

    if (result.success) {
      sendSuccess(res, {
        success: true,
        newBalance: result.newBalance,
        amount,
        transactionId: result.transactionId,
        message: result.message
      }, 'Transfer completed successfully');
    } else {
      sendError(res, result.message || 'Transfer failed', 400);
    }

  } catch (error) {
    logger.error('Error transferring to wallet', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to transfer money', 500);
  }
};

/**
 * Search for users available for wallet transfers
 */
export const searchUsers = async (req: Request & { userId?: string }, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return sendUnauthorized(res, 'Authentication required');
    }

    const { search = '' } = req.query;

    // Search for users
    const users = await searchTransferRecipients(
      parseInt(userId),
      search as string
    );

    sendSuccess(res, users, 'Users retrieved successfully');

  } catch (error) {
    logger.error('Error searching users', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.userId
    });
    sendError(res, 'Failed to search users', 500);
  }
};
