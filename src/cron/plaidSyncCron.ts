import cron from 'node-cron';
import { syncAllPendingTransfers, getSyncStatistics } from '../services/plaidStatusSyncService';
import logger from '../utils/logger';

/**
 * Set up cron jobs for Plaid transfer synchronization
 * This ensures no transfers are missed even if webhooks fail
 */
export function setupPlaidSyncCron() {
  // Sync pending transfers every 5 minutes
  cron.schedule('*/5 * * * *', async () => {
    try {
      logger.info('Starting scheduled Plaid transfer sync');
      const result = await syncAllPendingTransfers();
      
      logger.info('Scheduled sync completed', {
        synced: result.synced,
        confirmed: result.confirmed,
        failed: result.failed,
        errors: result.errors,
        totalProcessed: result.totalProcessed
      });
    } catch (error) {
      logger.error('Error in scheduled Plaid sync', { error });
    }
  }, {
    scheduled: true,
    timezone: 'UTC'
  });

  // Get sync statistics every hour
  cron.schedule('0 * * * *', async () => {
    try {
      const stats = await getSyncStatistics();
      logger.info('Plaid sync statistics', stats);
    } catch (error) {
      logger.error('Error getting sync statistics', { error });
    }
  }, {
    scheduled: true,
    timezone: 'UTC'
  });

  logger.info('Plaid sync cron jobs configured');
}

/**
 * Manual sync function for testing
 */
export async function manualSync() {
  try {
    logger.info('Starting manual Plaid transfer sync');
    const result = await syncAllPendingTransfers();
    
    logger.info('Manual sync completed', result);
    return result;
  } catch (error) {
    logger.error('Error in manual sync', { error });
    throw error;
  }
} 