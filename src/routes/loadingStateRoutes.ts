import express from 'express';
import { Request, Response } from 'express';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';
import { 
  createStaffLoadingSkeleton, 
  createPaymentsLoadingSkeleton,
  createDuesLoadingSkeleton,
  createWalletLoadingSkeleton,
  sendLoadingResponse, 
  LOADING_MESSAGES 
} from '../utils/loadingStates';
import { sendSuccess } from '../utils/response';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateTokenAndSession);

/**
 * @route GET /api/loading-states/staff
 * @desc Get staff module loading skeleton
 * @access Private
 */
router.get('/staff', (req: Request, res: Response) => {
  const skeleton = createStaffLoadingSkeleton();
  skeleton.loading.loadingMessage = LOADING_MESSAGES.STAFF.OVERVIEW;
  sendLoadingResponse(res, skeleton, LOADING_MESSAGES.STAFF.OVERVIEW);
});

/**
 * @route GET /api/loading-states/payments
 * @desc Get payments module loading skeleton
 * @access Private
 */
router.get('/payments', (req: Request, res: Response) => {
  const skeleton = createPaymentsLoadingSkeleton();
  skeleton.loading.loadingMessage = LOADING_MESSAGES.PAYMENTS.OVERVIEW;
  sendLoadingResponse(res, skeleton, LOADING_MESSAGES.PAYMENTS.OVERVIEW);
});

/**
 * @route GET /api/loading-states/dues
 * @desc Get dues module loading skeleton
 * @access Private
 */
router.get('/dues', (req: Request, res: Response) => {
  const skeleton = createDuesLoadingSkeleton();
  skeleton.loading.loadingMessage = LOADING_MESSAGES.DUES.OVERVIEW;
  sendLoadingResponse(res, skeleton, LOADING_MESSAGES.DUES.OVERVIEW);
});

/**
 * @route GET /api/loading-states/wallet
 * @desc Get wallet module loading skeleton
 * @access Private
 */
router.get('/wallet', (req: Request, res: Response) => {
  const skeleton = createWalletLoadingSkeleton();
  skeleton.loading.loadingMessage = LOADING_MESSAGES.WALLET.OVERVIEW;
  sendLoadingResponse(res, skeleton, LOADING_MESSAGES.WALLET.OVERVIEW);
});

/**
 * @route GET /api/loading-states/demo/:module
 * @desc Demo endpoint showing loading state followed by actual data
 * @access Private
 */
router.get('/demo/:module', async (req: Request, res: Response) => {
  const { module } = req.params;
  const { delay = '2000' } = req.query;
  
  try {
    let skeleton;
    let actualData;
    
    switch (module) {
      case 'staff':
        skeleton = createStaffLoadingSkeleton();
        skeleton.loading.loadingMessage = LOADING_MESSAGES.STAFF.OVERVIEW;
        actualData = {
          summaryCards: [
            { value: 25, trend: { direction: 'up', value: '12%' } },
            { value: '$15,420.50', trend: { direction: 'up', value: '8%' } },
            { value: '$2,340.00', trend: { direction: 'down', value: '3%' } },
            { value: '$120.00', trend: { direction: 'down', value: '15%' } }
          ],
          recentItems: [
            { id: 1, name: 'John Doe', amount: 500, date: '2024-01-15', type: 'payment' },
            { id: 2, name: 'Jane Smith', amount: 750, date: '2024-01-14', type: 'payment' },
            { id: 3, name: 'Bob Johnson', amount: 300, date: '2024-01-13', type: 'payment' }
          ]
        };
        break;
        
      case 'payments':
        skeleton = createPaymentsLoadingSkeleton();
        skeleton.loading.loadingMessage = LOADING_MESSAGES.PAYMENTS.OVERVIEW;
        actualData = {
          summaryCards: [
            { value: 156, trend: { direction: 'up', value: '5%' } },
            { value: 142, trend: { direction: 'up', value: '10%' } },
            { value: 14, trend: { direction: 'down', value: '2%' } },
            { value: '$8,750.00', trend: { direction: 'up', value: '15%' } }
          ],
          recentItems: [
            { id: 1, player: 'Alice Cooper', amount: 100, date: '2024-01-15', status: 'Paid' },
            { id: 2, player: 'David Wilson', amount: 100, date: '2024-01-14', status: 'Paid' },
            { id: 3, player: 'Emma Davis', amount: 100, date: '2024-01-13', status: 'Paid' }
          ]
        };
        break;
        
      case 'dues':
        skeleton = createDuesLoadingSkeleton();
        skeleton.loading.loadingMessage = LOADING_MESSAGES.DUES.OVERVIEW;
        actualData = {
          summaryCards: [
            { value: 89, trend: { direction: 'neutral', value: '0%' } },
            { value: '$7,200.00', trend: { direction: 'up', value: '12%' } },
            { value: '$1,800.00', trend: { direction: 'down', value: '5%' } },
            { value: '$450.00', trend: { direction: 'down', value: '8%' } }
          ],
          recentItems: [
            { id: 1, player: 'Mike Johnson', amount: 150, date: '2024-01-15', status: 'Paid' },
            { id: 2, player: 'Sarah Brown', amount: 150, date: '2024-01-14', status: 'Paid' },
            { id: 3, player: 'Tom Wilson', amount: 150, date: '2024-01-13', status: 'Paid' }
          ]
        };
        break;
        
      case 'wallet':
        skeleton = createWalletLoadingSkeleton();
        skeleton.loading.loadingMessage = LOADING_MESSAGES.WALLET.OVERVIEW;
        actualData = {
          summaryCards: [
            { value: '$2,450.75', trend: { direction: 'up', value: '3%' } },
            { value: '$125.00', trend: { direction: 'neutral', value: '0%' } },
            { value: 47, trend: { direction: 'up', value: '18%' } },
            { value: '$890.25', trend: { direction: 'up', value: '22%' } }
          ],
          recentItems: [
            { id: 1, description: 'Payment received', amount: 500, date: '2024-01-15', type: 'credit' },
            { id: 2, description: 'Transfer to bank', amount: -200, date: '2024-01-14', type: 'debit' },
            { id: 3, description: 'Dues payment', amount: -150, date: '2024-01-13', type: 'debit' }
          ]
        };
        break;
        
      default:
        return res.status(400).json({ error: 'Invalid module. Use: staff, payments, dues, or wallet' });
    }
    
    // Simulate loading delay
    await new Promise(resolve => setTimeout(resolve, parseInt(delay as string)));
    
    // Return actual data with loading state set to false
    skeleton.loading.isLoading = false;
    skeleton.summaryCards = skeleton.summaryCards.map((card, index) => ({
      ...card,
      isLoading: false,
      value: actualData.summaryCards[index]?.value || card.value,
      trend: {
        ...card.trend,
        isLoading: false,
        direction: actualData.summaryCards[index]?.trend?.direction || card.trend.direction,
        value: actualData.summaryCards[index]?.trend?.value || card.trend.value
      }
    }));
    
    if (skeleton.recentItems) {
      skeleton.recentItems.isLoading = false;
    }
    
    if (skeleton.chart) {
      skeleton.chart.isLoading = false;
    }
    
    const response = {
      ...skeleton,
      actualData
    };
    
    sendSuccess(res, response, `${module} data loaded successfully`);
    
  } catch (error) {
    res.status(500).json({ error: 'Failed to load demo data' });
  }
});

/**
 * @route GET /api/loading-states/all
 * @desc Get all module loading skeletons
 * @access Private
 */
router.get('/all', (req: Request, res: Response) => {
  const allSkeletons = {
    staff: createStaffLoadingSkeleton(),
    payments: createPaymentsLoadingSkeleton(),
    dues: createDuesLoadingSkeleton(),
    wallet: createWalletLoadingSkeleton()
  };
  
  // Set appropriate loading messages
  allSkeletons.staff.loading.loadingMessage = LOADING_MESSAGES.STAFF.OVERVIEW;
  allSkeletons.payments.loading.loadingMessage = LOADING_MESSAGES.PAYMENTS.OVERVIEW;
  allSkeletons.dues.loading.loadingMessage = LOADING_MESSAGES.DUES.OVERVIEW;
  allSkeletons.wallet.loading.loadingMessage = LOADING_MESSAGES.WALLET.OVERVIEW;
  
  sendSuccess(res, allSkeletons, 'All loading skeletons retrieved successfully');
});

export default router;
