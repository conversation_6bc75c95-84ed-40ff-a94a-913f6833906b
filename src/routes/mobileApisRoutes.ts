import { Router } from 'express';
import { listBankAccounts, addUpdateBankAccount, listTransactionsHistory, listWallet, setUpPin, changeWalletPin, resetWalletPin, sendOtp, verifyOTP } from '../controllers/appControllers/bankController';
import { addMoney, getAuthToken, getPlaidKeys, withdrawMoney } from '../controllers/appControllers/walletController';
import { authenticateTokenAndSession } from '../middlewares/sessionAuth';
import { sendPinResetOTP } from '../controllers/walletController';

const router = Router();
router.get('/get-auth-token', getAuthToken);
// All wallet routes require full authentication (JWT + session)
router.use(authenticateTokenAndSession);
router.get('/send-otp', sendOtp); // Send OTP for PIN reset
router.post('/verify-otp', verifyOTP); // Verify OTP for PIN reset
router.post('/add-update-bank', addUpdateBankAccount);
router.get('/list-bank-accounts', listBankAccounts);
router.get('/transaction-history', listTransactionsHistory);
router.get('/get-wallet', listWallet);
router.post('/add-pin',setUpPin)
router.post('/change-pin', changeWalletPin);
router.post('/send-pin-reset-otp', sendPinResetOTP);
router.post('/reset-pin', resetWalletPin);


router.post('/add-money', addMoney); // Requires PIN and uses primary bank
router.post('/withdraw', withdrawMoney); // Requires PIN and transfers to primary bank

router.get('/get-keys', getPlaidKeys)

export default router;
