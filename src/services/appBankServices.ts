import { executeQuery, executeQ<PERSON>y<PERSON>ingle, executeUpdate } from '../utils/database';
import logger from '../utils/logger';
import { getPaymentMethodById } from './paymentMethodService';
import { getPrimaryBankAccount, getUserMasterWallet, verifyWalletMasterPin } from './walletService';

/**
 * Add or update a bank account for the user
 * @param params - Bank account details (id for update, rest for insert)
 * @returns Promise<{ updated: boolean, inserted: boolean }>
 */
export async function addOrUpdateBankAccount(params: {
  id?: string;
  bank_name: string;
  account_number_last4: string;
  routing_number: string;
  account_type: string;
  userId: string;
  plaid_account_id?: string;
  account_mask?: string;
  plaid_item_id?: string;
  plaid_access_token?: string;
}): Promise<{ updated?: boolean; inserted?: boolean }> {
  try {
    const { id, bank_name,plaid_account_id,account_mask, account_number_last4, routing_number, account_type,  userId ,  plaid_access_token,plaid_item_id } = params;
    if (id) {
      // Update existing bank account
      const account = await executeQuerySingle('SELECT * FROM tbl_staff_bank_accounts WHERE id = ? AND staff_id = ?', [id, userId]);
      if (!account) {
        return { updated: false };
      }
      await executeUpdate(
        `UPDATE tbl_bank_accounts SET bank_name = ?, account_number_last4 = ?, routing_number = ?, account_type = ? WHERE id = ? AND staff_id = ?`,
        [bank_name, account_number_last4, routing_number, account_type,  id, userId]
      );
      return { updated: true };
    } else {
      // Insert new bank account
      await executeUpdate(
            `INSERT INTO tbl_bank_accounts
           (user_id, plaid_account_id, account_mask, bank_name, account_type, routing_number, account_number_last4, is_primary, plaid_access_token, plaid_item_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        // `INSERT INTO tbl_staff_bank_accounts (staff_id, bank_name, account_number, routing_number, account_type, account_holder_name) VALUES (?, ?, ?, ?, ?, ?)`,
        [userId,plaid_account_id,account_mask, bank_name,account_type,routing_number, account_number_last4,  1, plaid_access_token,plaid_item_id ]
      );
      return { inserted: true };
    }
  } catch (error) {
    logger.error('Error in addOrUpdateBankAccount', { error });
    throw error;
  }
}

/**
 * List all bank accounts for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getBankAccounts(userId: string): Promise<any[]> {
  try {
    const accounts = await executeQuery('SELECT * FROM tbl_bank_accounts WHERE user_id = ? ORDER BY id DESC, created_at ASC', [userId]);
    return accounts;
  } catch (error) {
    logger.error('Error in getBankAccounts', { error });
    throw error;
  }
}

/**
 * List all wallet transactions for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getTransactionsHistory(userId: string): Promise<any[]> {
  try {
    const transactions = await executeQuery(
      `SELECT * FROM tbl_wallet_transactions WHERE user_id = ? ORDER BY id DESC, created_at ASC`,
      [userId]
    );
    return transactions;
  } catch (error) {
    logger.error('Error in getTransactionsHistory', { error });
    throw error;
  }
}

/**
 * List wallet info for the user
 * @param userId - User ID
 * @returns Promise<any[]>
 */
export async function getWallets(userId: string): Promise<any[]> {
  try {
    const wallets = await executeQuery(
      `SELECT id , user_id , wallet_unique_id , name , username , balance FROM tbl_masterwallet WHERE user_id = ? ORDER BY id DESC, created_at ASC`,
      [userId]
    );
    return wallets;
  } catch (error) {
    logger.error('Error in getWallets', { error });
    throw error;
  }
}

export async function setPin(userId: string, pin: string): Promise<void> {
  try {
    await executeUpdate(
      `UPDATE tbl_masterwallet SET wallet_master_pin = ? WHERE user_id = ?`,
      [pin, userId]
    );
  } catch (error) {
    logger.error('Error in setUpPin', { error });
    throw error;
  }
}
export async function createWalletTransaction(
  userId: number,
  amount: number,
  description: string,
  type: string = 'wallet_transfer',
  referenceId?: string,
  metaData?: any,
  statusId: number = 1, // Default to completed (1), can be set to pending (2) or failed (3)
  plaidEventId?: string
): Promise<number | null> {
  try {
    const result = await executeUpdate(
      `INSERT INTO tbl_wallet_transactions
       (user_id, type, amount, reference_id, payment_provider, description, status_id, created_at, meta_data, plaid_event_id)
       VALUES (?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?)`,
      [
        userId,
        type,
        amount,
        referenceId || `TXN_${Date.now()}_${userId}`,
        type,
        description,
        statusId,
        metaData ? JSON.stringify(metaData) : null,
        plaidEventId || null
      ]
    );

    return result && result.insertId ? result.insertId : null;
  } catch (error) {
    logger.error('Error creating wallet transaction', { userId, amount, type, error });
    return null;
  }
}



export async function addMoneyFromBank(
  userId: number,
  amount: number,
  pin: string,
  bankAccountId?: string
): Promise<{ success: boolean, newBalance?: number, message?: string, transactionId?: number }> {
  try {
    // Get user's wallet
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      return { success: false, message: 'Wallet not found' };
    }

    // Verify PIN
    const isPinValid = await verifyWalletMasterPin(userId, pin);
    if (!isPinValid) {
      return { success: false, message: 'Invalid PIN' };
    }

    // Get bank account
    let bankAccount= {
      "id":"22",
      "bank_name": "Bank of Tests",
      "bank_account_number": "",
      "account_mask":"1111"
    };
    if (bankAccountId) {
      bankAccount = await executeQuerySingle(
        'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
        [bankAccountId, userId]
      );
    } else {
      bankAccount = await getPrimaryBankAccount(userId);
    }
    // bankAccount = {
    //   "id":"22",
    //   "bank_name": "Bank of Tests",
    //   "bank_account_number": "",
    //   "account_mask":"1111"
    // };

    if (!bankAccount) {
      return { success: false, message: 'Bank account not found' };
    }

    // Use Plaid for real-time transfer
    try {
      // Import the Plaid transfer service and enhanced pending balance service
      const { createACHDebitTransferFormobile } = await import('./plaidTransferService');
      const { createPendingDeposit, getEnhancedWalletBalance } = await import('./enhancedPendingBalanceService');

      // Create the transfer using Plaid
      const plaidResult = await createACHDebitTransferFormobile(
        userId,
        amount,
        `Add money to wallet from ${bankAccount.bank_name}`,
        bankAccount.id.toString()
      );

      if (!plaidResult.success) {
        return { success: false, message: plaidResult.message || 'Failed to add money from bank' };
      }

      // Create pending deposit instead of immediately updating balance
      const pendingResult = await createPendingDeposit(
        userId,
        amount,
        plaidResult.transferId || `ADD_MONEY_${Date.now()}`,
        `Add money from ${bankAccount.bank_name}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          plaidTransferId: plaidResult.transferId,
          plaidStatus: plaidResult.status
        }
      );

      if (!pendingResult.success) {
        return { success: false, message: 'Failed to create pending deposit' };
      }

      // Create transaction record with pending status
      const transactionId = await createWalletTransaction(
        userId,
        amount,
        `Add money from ${bankAccount.bank_name}`,
        'bank_transfer',
        plaidResult.transferId || `ADD_MONEY_${Date.now()}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          plaidTransferId: plaidResult.transferId,
          plaidStatus: plaidResult.status,
          holdId: pendingResult.holdId
        },
        2 // status_id = 2 for pending
      );

      // Get current balance info for response
      const balanceInfo = await getEnhancedWalletBalance(userId);
      const currentBalance = balanceInfo?.balance || 0;

      logger.info('Pending deposit created for add money', {
        userId,
        amount,
        plaidTransferId: plaidResult.transferId,
        transactionId,
        holdId: pendingResult.holdId
      });

      return {
        success: true,
        newBalance: currentBalance, // Return current balance, not updated balance
        transactionId,
        message: `Transfer initiated. ${amount.toFixed(2)} will be added to your wallet once confirmed by your bank.`
      };
    } catch (error) {
      logger.error('Error adding money from bank', { userId, amount, error });
      return { success: false, message: 'Failed to add money from bank' };
    }
  } catch (error) {
    logger.error('Error adding money from bank', { userId, amount, error });
    return { success: false, message: 'Failed to add money from bank' };
  }
}

export async function withdrawMoneyToBank(
  userId: number,
  amount: number,
  pin: string,
  bankAccountId?: string,
  paymentMethod: string = 'ach_standard'
): Promise<{ success: boolean, newBalance?: number, message?: string, transactionId?: number }> {
  try {
    logger.info('Starting withdrawal process', { userId, amount, paymentMethod, bankAccountId });

    // Validate amount format - must be a positive number with exactly 2 decimal places
    const amountStr = amount.toString();
    const decimalParts = amountStr.split('.');
    if (amount <= 0 || decimalParts.length > 2 || (decimalParts.length === 2 && decimalParts[1].length !== 2)) {
      logger.warn('Invalid amount format for withdrawal', { userId, amount });
      return { success: false, message: 'Withdrawal failed: amount must be a decimal with 2 places greater than 0, such as 0.10' };
    }

    // Format amount to ensure exactly 2 decimal places
    const formattedAmount = parseFloat(amount.toFixed(2));

    // Get user's wallet
    const wallet = await getUserMasterWallet(userId);
    if (!wallet) {
      logger.error('Wallet not found for user', { userId });
      return { success: false, message: 'Wallet not found' };
    }
    logger.info('Wallet retrieved successfully', { userId, walletId: wallet.id });

    // Verify PIN
    const { comparePin } = await import('../models/user');
    const isPinValid = await comparePin(pin, wallet.wallet_master_pin || '');
    if (!isPinValid) {
      logger.warn('Invalid PIN attempt for withdrawal', { userId });
      return { success: false, message: 'Invalid PIN' };
    }
    logger.info('PIN verification successful', { userId });

    // Import enhanced balance service
    const { getEnhancedWalletBalance, createWithdrawalHold } = await import('./enhancedPendingBalanceService');

    // Check sufficient available balance (not just main balance)
    const balanceInfo = await getEnhancedWalletBalance(userId);
    if (!balanceInfo) {
      logger.error('Failed to get wallet balance info', { userId });
      return { success: false, message: 'Failed to check wallet balance' };
    }

    const currentBalance = balanceInfo.balance;
    const availableBalance = balanceInfo.available_balance;

    if (availableBalance < formattedAmount) {
      logger.warn('Insufficient available balance for withdrawal', {
        userId,
        availableBalance,
        requestedAmount: formattedAmount,
        blockedBalance: balanceInfo.blocked_balance
      });
      return { success: false, message: 'Insufficient available balance' };
    }
    logger.info('Available balance check passed', { userId, availableBalance, requestedAmount: formattedAmount });

    // Get selected bank account or primary bank account
    let  bankAccount = {
      "id":"22",
      "bank_name": "Bank of Tests",
      "bank_account_number": "",
      "account_mask":"1111"
    };
    if (bankAccountId) {
      logger.info('Retrieving specific bank account', { userId, bankAccountId });
      // Get specific bank account by ID
      bankAccount = await executeQuerySingle(
        'SELECT * FROM tbl_bank_accounts WHERE id = ? AND user_id = ?',
        [bankAccountId, userId]
      );
      if (!bankAccount) {
        logger.error('Selected bank account not found', { userId, bankAccountId });
        return { success: false, message: 'Selected bank account not found or does not belong to user.' };
      }
    } else {
      logger.info('Retrieving primary bank account', { userId });
      // Fall back to primary bank account
      bankAccount = await getPrimaryBankAccount(userId);
      if (!bankAccount) {
        logger.error('No primary bank account found', { userId });
        return { success: false, message: 'No primary bank account found. Please connect a bank account first.' };
      }
    }
    
    logger.info('Bank account retrieved successfully', { userId, bankAccountId: bankAccount.id, bankName: bankAccount.bank_name });

    // Get payment method details from database
    logger.info('Retrieving payment method', { paymentMethod });
    const selectedMethod = await getPaymentMethodById(paymentMethod);
    if (!selectedMethod) {
      logger.error('Invalid payment method selected', { paymentMethod });
      return { success: false, message: 'Invalid payment method selected' };
    }
    logger.info('Payment method retrieved successfully', { paymentMethod, methodName: selectedMethod.name, fee: selectedMethod.fee });

    // Calculate processing fee using the new fee calculation logic
    const { calculatePaymentMethodFee } = await import('./paymentMethodService');
    const processingFee = calculatePaymentMethodFee(selectedMethod, amount);

    // Ensure limits are numbers
    const minAmount = parseFloat(selectedMethod.min_amount?.toString() || '0') || 0;
    const maxAmount = parseFloat(selectedMethod.max_amount?.toString() || '0') || 0;

    const totalAmount = amount + processingFee;

    // Check if user has sufficient available balance including fees
    if (availableBalance < totalAmount) {
      return {
        success: false,
        message: `Insufficient available balance. Total amount including ${processingFee > 0 ? `${processingFee.toFixed(2)} fee` : 'no fee'}: ${totalAmount.toFixed(2)}`
      };
    }

    // Validate amount limits
    if (amount < minAmount) {
      return {
        success: false,
        message: `Minimum withdrawal amount for ${selectedMethod.name} is ${minAmount.toFixed(2)}`
      };
    }

    if (maxAmount > 0 && amount > maxAmount) {
      return {
        success: false,
        message: `Maximum withdrawal amount for ${selectedMethod.name} is ${maxAmount.toFixed(2)}`
      };
    }

    // Use Plaid for real-time transfer
    logger.info('Initiating Plaid transfer for withdrawal', { userId, amount, bankAccountId: bankAccount.id });

    try {
      // Import the Plaid transfer service
      const { createACHCreditTransfer } = await import('./plaidTransferService');

      // Convert payment method to Plaid payment method type
      let plaidPaymentMethodType = 'ach';
      if (paymentMethod.includes('wire')) {
        plaidPaymentMethodType = 'wire';
      } else if (paymentMethod.includes('instant')) {
        plaidPaymentMethodType = 'instant';
      }

      // Create the transfer using Plaid
      const plaidResult = await createACHCreditTransfer(
        userId,
        amount,
        `${selectedMethod.name} withdrawal to ${bankAccount.bank_name}`,
        bankAccount.id.toString(),
        plaidPaymentMethodType
      );

      if (!plaidResult.success) {
        logger.error('Plaid transfer failed', { userId, error: plaidResult.message });
        return { success: false, message: plaidResult.message || 'Failed to initiate withdrawal' };
      }

      logger.info('Plaid transfer initiated successfully', {
        userId,
        transferId: plaidResult.transferId,
        status: plaidResult.status
      });

      // Create withdrawal hold instead of immediately updating balance
      const holdResult = await createWithdrawalHold(
        userId,
        totalAmount,
        plaidResult.transferId || `WITHDRAW_${Date.now()}`,
        `${selectedMethod.name} withdrawal to ${bankAccount.bank_name}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          paymentMethod: paymentMethod,
          paymentMethodName: selectedMethod.name,
          withdrawalAmount: amount,
          processingFee: processingFee,
          totalAmount: totalAmount,
          estimatedArrival: selectedMethod.processing_time,
          plaidTransferId: plaidResult.transferId,
          plaidStatus: plaidResult.status
        }
      );

      if (!holdResult.success) {
        logger.error('Failed to create withdrawal hold', { userId, error: holdResult.message });
        return { success: false, message: 'Failed to process withdrawal' };
      }

      // Create transaction record with pending status
      const transactionId = await createWalletTransaction(
        userId,
        -totalAmount, // Negative total amount including fees for withdrawal
        `${selectedMethod.name} withdrawal to ${bankAccount.bank_name}`,
        'bank_transfer',
        plaidResult.transferId || `WITHDRAW_${Date.now()}`,
        {
          bankAccountId: bankAccount.id,
          bankName: bankAccount.bank_name,
          accountMask: bankAccount.account_mask,
          paymentMethod: paymentMethod,
          paymentMethodName: selectedMethod.name,
          withdrawalAmount: amount,
          processingFee: processingFee,
          totalAmount: totalAmount,
          estimatedArrival: selectedMethod.processing_time,
          status: plaidResult.status || 'pending',
          plaidTransferId: plaidResult.transferId,
          plaidStatus: plaidResult.status,
          holdId: holdResult.holdId
        },
        2 // status_id = 2 for pending
      );

      if (!transactionId) {
        logger.error('Failed to create transaction record', { userId });
        return { success: false, message: 'Failed to create transaction record' };
      }
      logger.info('Transaction record created successfully', { userId, transactionId });

      // Get updated balance info for response
      const updatedBalanceInfo = await getEnhancedWalletBalance(userId);
      const newAvailableBalance = updatedBalanceInfo?.available_balance || 0;

      logger.info('Withdrawal hold created successfully', {
        userId,
        transactionId,
        holdId: holdResult.holdId,
        availableBalance: newAvailableBalance
      });

      // Initialize withdrawal progress tracking
      logger.info('Initializing withdrawal progress tracking', { transactionId });
      try {
        const progressInitialized = await initializeWithdrawalProgress(transactionId);
        if (!progressInitialized) {
          logger.warn('Failed to initialize withdrawal progress, but withdrawal was successful', {
            transactionId,
            message: 'Progress tracking initialization returned false'
          });
        } else {
          logger.info('Withdrawal progress tracking initialized successfully', { transactionId });
        }
      } catch (progressError) {
        logger.warn('Failed to initialize withdrawal progress, but withdrawal was successful', {
          transactionId,
          progressError: progressError instanceof Error ? progressError.message : 'Unknown error',
          errorStack: progressError instanceof Error ? progressError.stack : undefined,
          sqlState: (progressError as any)?.sqlState,
          sqlMessage: (progressError as any)?.sqlMessage
        });
        // Don't fail the entire withdrawal if progress tracking fails
      }

      logger.info('Withdrawal initiated with hold', {
        userId,
        amount,
        totalAmount,
        paymentMethod,
        fee: processingFee,
        availableBalance: newAvailableBalance,
        transactionId,
        holdId: holdResult.holdId,
        bankAccount: bankAccount.bank_name
      });

      return {
        success: true,
        newBalance: newAvailableBalance, // Return available balance, not main balance
        transactionId,
        message: `Withdrawal initiated. ${amount.toFixed(2)} will be sent to ${bankAccount.bank_name} once confirmed. ${processingFee > 0 ? `Processing fee: ${processingFee.toFixed(2)}. ` : ''}Expected arrival: ${selectedMethod.processing_time}.`
      };
    } catch (error) {
      logger.error('Error with Plaid transfer', { userId, amount, error });
      return { success: false, message: 'Failed to initiate withdrawal with Plaid' };
    }
  } catch (error) {
    logger.error('Error withdrawing money to bank', {
      userId,
      amount,
      error: error instanceof Error ? error.message : 'Unknown error',
      errorStack: error instanceof Error ? error.stack : undefined,
      errorName: error instanceof Error ? error.name : undefined,
      sqlState: (error as any)?.sqlState,
      sqlMessage: (error as any)?.sqlMessage,
      errno: (error as any)?.errno,
      code: (error as any)?.code
    });
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Failed to withdraw money. Please try again.'
    };
  }
}