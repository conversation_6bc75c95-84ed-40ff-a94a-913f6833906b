import { executeQuery, executeUpdate } from '../utils/database';
import { 
  confirmPendingDeposit, 
  confirmWithdrawalHold, 
  releaseFailedHold,
  cleanupExpiredHolds 
} from './enhancedPendingBalanceService';
import { getTransferStatus, updateTransferStatus } from './plaidTransferService';
import logger from '../utils/logger';

/**
 * Sync all pending transfers with Plaid to catch any missed webhook events
 * This ensures no transfers are stuck in pending status
 */
export async function syncAllPendingTransfers(): Promise<{ 
  synced: number; 
  confirmed: number; 
  failed: number; 
  errors: number; 
  totalProcessed: number;
}> {
  try {
    logger.info('Starting comprehensive sync of all pending transfers');

    // Get all pending holds that haven't been confirmed or failed
    const pendingHolds = await executeQuery(
      `SELECT * FROM tbl_pending_holds 
       WHERE status = 'pending' 
       AND plaid_transfer_id IS NOT NULL 
       AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
       ORDER BY created_at ASC`
    );

    // Also get pending wallet transactions
    const pendingTransactions = await executeQuery(
      `SELECT * FROM tbl_wallet_transactions 
       WHERE payment_provider LIKE 'plaid_%'
       AND status_id = 2
       AND created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
       ORDER BY created_at ASC`
    );

    let synced = 0;
    let confirmed = 0;
    let failed = 0;
    let errors = 0;
    let totalProcessed = 0;

    // Process pending holds
    for (const hold of pendingHolds) {
      totalProcessed++;
      try {
        const transferId = hold.plaid_transfer_id;
        
        if (!transferId) {
          logger.warn('Hold has no plaid_transfer_id', { holdId: hold.id });
          continue;
        }
        
        // Get current status from Plaid
        const statusResult = await getTransferStatus(transferId);
        
        if (!statusResult.success) {
          logger.warn('Failed to get transfer status from Plaid', {
            transferId,
            holdId: hold.id,
            error: statusResult.message
          });
          errors++;
          continue;
        }

        synced++;

        // Process based on status
        const plaidStatus = statusResult.status;
        
        if (plaidStatus === 'posted' || plaidStatus === 'settled') {
          // Transfer completed successfully
          if (hold.type === 'deposit') {
            const result = await confirmPendingDeposit(transferId, plaidStatus);
            if (result.success) {
              confirmed++;
              logger.info('Deposit confirmed via sync', {
                transferId,
                holdId: hold.id,
                userId: result.userId,
                amount: result.amount
              });
            }
          } else if (hold.type === 'withdrawal') {
            const result = await confirmWithdrawalHold(transferId, plaidStatus);
            if (result.success) {
              confirmed++;
              logger.info('Withdrawal confirmed via sync', {
                transferId,
                holdId: hold.id,
                userId: result.userId,
                amount: result.amount
              });
            }
          }
        } else if (plaidStatus === 'failed' || plaidStatus === 'cancelled' || plaidStatus === 'returned' || plaidStatus === 'rejected') {
          // Transfer failed
          const result = await releaseFailedHold(transferId, statusResult.failureReason || 'Transfer failed');
          if (result.success) {
            failed++;
            logger.info('Failed hold released via sync', {
              transferId,
              holdId: hold.id,
              userId: result.userId,
              amount: result.amount
            });
          }
        } else {
          // Update status but don't process yet
          await updateTransferStatus(transferId, plaidStatus, statusResult.failureReason);
          logger.info('Transfer status updated via sync', {
            transferId,
            status: plaidStatus
          });
        }
      } catch (error) {
        logger.error('Error syncing transfer status', {
          transferId: hold.plaid_transfer_id,
          holdId: hold.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        errors++;
      }
    }

    // Process pending wallet transactions
    for (const transaction of pendingTransactions) {
      totalProcessed++;
      try {
        const transferId = transaction.reference_id;
        
        if (!transferId) {
          logger.warn('Transaction has no reference_id', { transactionId: transaction.id });
          continue;
        }
        
        // Skip if we already processed this transfer via holds
        const alreadyProcessed = pendingHolds.some(hold => hold.plaid_transfer_id === transferId);
        if (alreadyProcessed) {
          continue;
        }
        
        // Get current status from Plaid
        const statusResult = await getTransferStatus(transferId);
        
        if (!statusResult.success) {
          logger.warn('Failed to get transfer status from Plaid for transaction', {
            transferId,
            transactionId: transaction.id,
            error: statusResult.message
          });
          errors++;
          continue;
        }

        synced++;

        // Update transaction status based on Plaid status
        const plaidStatus = statusResult.status;
        
        if (plaidStatus === 'posted' || plaidStatus === 'settled') {
          await executeUpdate(
            `UPDATE tbl_wallet_transactions 
             SET status_id = '1', plaid_status = ?, plaid_status_updated_at = NOW()
             WHERE id = ?`,
            [plaidStatus, transaction.id]
          );
          confirmed++;
          logger.info('Transaction status updated to completed via sync', {
            transferId,
            transactionId: transaction.id,
            status: plaidStatus
          });
        } else if (plaidStatus === 'failed' || plaidStatus === 'cancelled' || plaidStatus === 'returned' || plaidStatus === 'rejected') {
          await executeUpdate(
            `UPDATE tbl_wallet_transactions 
             SET status_id = '3', plaid_status = ?, plaid_status_updated_at = NOW()
             WHERE id = ?`,
            [plaidStatus, transaction.id]
          );
          failed++;
          logger.info('Transaction status updated to failed via sync', {
            transferId,
            transactionId: transaction.id,
            status: plaidStatus
          });
        } else {
          // Update status but don't change transaction status yet
          await executeUpdate(
            `UPDATE tbl_wallet_transactions 
             SET plaid_status = ?, plaid_status_updated_at = NOW()
             WHERE id = ?`,
            [plaidStatus, transaction.id]
          );
          logger.info('Transaction status updated via sync', {
            transferId,
            transactionId: transaction.id,
            status: plaidStatus
          });
        }
      } catch (error) {
        logger.error('Error syncing transaction status', {
          transferId: transaction.reference_id,
          transactionId: transaction.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        errors++;
      }
    }

    // Clean up expired holds
    const cleanupResult = await cleanupExpiredHolds();
    
    logger.info('Transfer sync completed', {
      synced,
      confirmed,
      failed,
      errors,
      totalProcessed,
      cleanedHolds: cleanupResult.cleaned
    });

    return {
      synced,
      confirmed,
      failed,
      errors,
      totalProcessed
    };

  } catch (error) {
    logger.error('Error in comprehensive transfer sync', { error });
    return {
      synced: 0,
      confirmed: 0,
      failed: 0,
      errors: 1,
      totalProcessed: 0
    };
  }
}

/**
 * Sync a specific transfer by ID
 */
export async function syncSpecificTransfer(transferId: string): Promise<{
  success: boolean;
  status?: string;
  message?: string;
}> {
  try {
    logger.info('Syncing specific transfer', { transferId });

    const statusResult = await getTransferStatus(transferId);
    
    if (!statusResult.success) {
      return {
        success: false,
        message: statusResult.message || 'Failed to get transfer status'
      };
    }

    const plaidStatus = statusResult.status;
    
    // Update transfer status in database
    await updateTransferStatus(transferId!, plaidStatus, statusResult.failureReason);
    
    // Process based on status
    if (plaidStatus === 'posted' || plaidStatus === 'settled') {
      // Try to confirm holds
      const withdrawalResult = await confirmWithdrawalHold(transferId!, plaidStatus);
      if (withdrawalResult.success) {
        return {
          success: true,
          status: plaidStatus,
          message: 'Withdrawal confirmed'
        };
      }
      
      const depositResult = await confirmPendingDeposit(transferId!, plaidStatus);
      if (depositResult.success) {
        return {
          success: true,
          status: plaidStatus,
          message: 'Deposit confirmed'
        };
      }
      
      return {
        success: true,
        status: plaidStatus,
        message: 'Transfer completed but no pending hold found'
      };
    } else if (plaidStatus === 'failed' || plaidStatus === 'cancelled' || plaidStatus === 'returned' || plaidStatus === 'rejected') {
      const releaseResult = await releaseFailedHold(transferId!, statusResult.failureReason || 'Transfer failed');
      return {
        success: true,
        status: plaidStatus,
        message: releaseResult.success ? 'Failed hold released' : 'Transfer failed'
      };
    }
    
    return {
      success: true,
      status: plaidStatus,
      message: 'Status updated'
    };

  } catch (error) {
    logger.error('Error syncing specific transfer', { transferId, error });
    return {
      success: false,
      message: 'Error syncing transfer'
    };
  }
}

/**
 * Record transfer status history for audit purposes
 */
async function recordTransferStatusHistory(
  transferId: string, 
  status: string, 
  failureReason?: string
): Promise<void> {
  try {
    await executeUpdate(
      `INSERT INTO tbl_plaid_transfer_status_history 
       (plaid_transfer_id, status, failure_reason, status_timestamp, webhook_received_at, metadata)
       VALUES (?, ?, ?, NOW(), NOW(), ?)`,
      [
        transferId,
        status,
        failureReason || null,
        JSON.stringify({ source: 'sync_service', timestamp: new Date().toISOString() })
      ]
    );
  } catch (error) {
    logger.warn('Failed to record transfer status history', {
      transferId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Get sync statistics
 */
export async function getSyncStatistics(): Promise<{
  pendingHolds: number;
  recentSyncs: number;
  failedTransfers: number;
  expiredHolds: number;
}> {
  try {
    const [pendingHolds, recentSyncs, failedTransfers, expiredHolds] = await Promise.all([
      executeQuery(`SELECT COUNT(*) as count FROM tbl_pending_holds WHERE status = 'pending'`),
      executeQuery(`SELECT COUNT(*) as count FROM tbl_plaid_transfer_status_history WHERE webhook_received_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)`),
      executeQuery(`SELECT COUNT(*) as count FROM tbl_pending_holds WHERE status = 'failed' AND updated_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)`),
      executeQuery(`SELECT COUNT(*) as count FROM tbl_pending_holds WHERE status = 'expired'`)
    ]);

    return {
      pendingHolds: pendingHolds[0]?.count || 0,
      recentSyncs: recentSyncs[0]?.count || 0,
      failedTransfers: failedTransfers[0]?.count || 0,
      expiredHolds: expiredHolds[0]?.count || 0
    };
  } catch (error) {
    logger.error('Error getting sync statistics', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    return {
      pendingHolds: 0,
      recentSyncs: 0,
      failedTransfers: 0,
      expiredHolds: 0
    };
  }
}
