import { Response } from 'express';
import logger from './logger';

/**
 * Standard loading state structure for summary cards
 */
export interface LoadingState {
  isLoading: boolean;
  loadingMessage?: string;
  shimmerConfig?: {
    cards: number;
    hasChart?: boolean;
    hasTable?: boolean;
    hasRecentItems?: boolean;
  };
}

/**
 * Summary card skeleton structure for consistent loading states
 */
export interface SummaryCardSkeleton {
  title: string;
  value: string | number;
  isLoading: boolean;
  shimmerType: 'number' | 'currency' | 'percentage' | 'text';
  icon?: string;
  trend?: {
    direction: 'up' | 'down' | 'neutral';
    value: string;
    isLoading: boolean;
  };
}

/**
 * Dashboard skeleton structure
 */
export interface DashboardSkeleton {
  loading: LoadingState;
  summaryCards: SummaryCardSkeleton[];
  recentItems?: {
    isLoading: boolean;
    count: number;
    itemType: 'transactions' | 'payments' | 'staff' | 'dues';
  };
  chart?: {
    isLoading: boolean;
    type: 'line' | 'bar' | 'pie' | 'donut';
  };
}

/**
 * Create loading state for summary cards
 */
export function createSummaryCardSkeleton(
  title: string,
  shimmerType: 'number' | 'currency' | 'percentage' | 'text' = 'number',
  icon?: string
): SummaryCardSkeleton {
  return {
    title,
    value: shimmerType === 'currency' ? '$0.00' : shimmerType === 'percentage' ? '0%' : '0',
    isLoading: true,
    shimmerType,
    icon,
    trend: {
      direction: 'neutral',
      value: '0%',
      isLoading: true
    }
  };
}

/**
 * Create staff module loading skeleton
 */
export function createStaffLoadingSkeleton(): DashboardSkeleton {
  return {
    loading: {
      isLoading: true,
      loadingMessage: 'Loading staff overview...',
      shimmerConfig: {
        cards: 4,
        hasTable: true,
        hasRecentItems: true
      }
    },
    summaryCards: [
      createSummaryCardSkeleton('Total Staff', 'number', 'users'),
      createSummaryCardSkeleton('Total Payments', 'currency', 'dollar-sign'),
      createSummaryCardSkeleton('Pending Payments', 'currency', 'clock'),
      createSummaryCardSkeleton('Failed Payments', 'currency', 'x-circle')
    ],
    recentItems: {
      isLoading: true,
      count: 5,
      itemType: 'transactions'
    }
  };
}

/**
 * Create payments module loading skeleton
 */
export function createPaymentsLoadingSkeleton(): DashboardSkeleton {
  return {
    loading: {
      isLoading: true,
      loadingMessage: 'Loading payment overview...',
      shimmerConfig: {
        cards: 4,
        hasChart: true,
        hasRecentItems: true
      }
    },
    summaryCards: [
      createSummaryCardSkeleton('Total Dues', 'number', 'file-text'),
      createSummaryCardSkeleton('Paid Dues', 'number', 'check-circle'),
      createSummaryCardSkeleton('Pending Dues', 'number', 'clock'),
      createSummaryCardSkeleton('Total Amount Due', 'currency', 'dollar-sign')
    ],
    recentItems: {
      isLoading: true,
      count: 5,
      itemType: 'payments'
    },
    chart: {
      isLoading: true,
      type: 'donut'
    }
  };
}

/**
 * Create dues module loading skeleton
 */
export function createDuesLoadingSkeleton(): DashboardSkeleton {
  return {
    loading: {
      isLoading: true,
      loadingMessage: 'Loading dues summary...',
      shimmerConfig: {
        cards: 4,
        hasTable: true,
        hasRecentItems: true
      }
    },
    summaryCards: [
      createSummaryCardSkeleton('Total Dues', 'number', 'file-text'),
      createSummaryCardSkeleton('Paid Amount', 'currency', 'check-circle'),
      createSummaryCardSkeleton('Pending Amount', 'currency', 'clock'),
      createSummaryCardSkeleton('Overdue Amount', 'currency', 'alert-circle')
    ],
    recentItems: {
      isLoading: true,
      count: 5,
      itemType: 'dues'
    }
  };
}

/**
 * Create wallet module loading skeleton
 */
export function createWalletLoadingSkeleton(): DashboardSkeleton {
  return {
    loading: {
      isLoading: true,
      loadingMessage: 'Loading wallet overview...',
      shimmerConfig: {
        cards: 4,
        hasChart: true,
        hasRecentItems: true
      }
    },
    summaryCards: [
      createSummaryCardSkeleton('Available Balance', 'currency', 'wallet'),
      createSummaryCardSkeleton('Pending Balance', 'currency', 'clock'),
      createSummaryCardSkeleton('Total Transactions', 'number', 'activity'),
      createSummaryCardSkeleton('This Month', 'currency', 'calendar')
    ],
    recentItems: {
      isLoading: true,
      count: 5,
      itemType: 'transactions'
    },
    chart: {
      isLoading: true,
      type: 'line'
    }
  };
}

/**
 * Send loading response with skeleton data
 */
export function sendLoadingResponse(
  res: Response,
  skeleton: DashboardSkeleton,
  message: string = 'Loading data...'
): void {
  const response = {
    success: true,
    message,
    data: skeleton,
    isLoading: true,
    timestamp: new Date().toISOString()
  };

  logger.info('Sending loading response', { message, skeletonType: skeleton.loading.loadingMessage });
  res.status(200).json(response);
}

/**
 * Transform actual data to match skeleton structure
 */
export function transformToSkeletonStructure(
  actualData: any,
  skeleton: DashboardSkeleton
): DashboardSkeleton {
  const transformed = { ...skeleton };
  
  // Mark as not loading
  transformed.loading.isLoading = false;
  
  // Update summary cards with actual data
  if (actualData.summaryCards) {
    transformed.summaryCards = transformed.summaryCards.map((card, index) => ({
      ...card,
      isLoading: false,
      value: actualData.summaryCards[index]?.value || card.value,
      trend: actualData.summaryCards[index]?.trend || { ...card.trend, isLoading: false }
    }));
  }
  
  // Update recent items
  if (transformed.recentItems && actualData.recentItems) {
    transformed.recentItems.isLoading = false;
  }
  
  // Update chart
  if (transformed.chart && actualData.chart) {
    transformed.chart.isLoading = false;
  }
  
  return transformed;
}

/**
 * Create loading middleware for consistent loading states
 */
export function withLoadingState(
  skeletonFactory: () => DashboardSkeleton,
  loadingMessage?: string
) {
  return (req: any, res: Response, next: any) => {
    // Add loading state helper to response
    res.sendLoading = () => {
      const skeleton = skeletonFactory();
      if (loadingMessage) {
        skeleton.loading.loadingMessage = loadingMessage;
      }
      sendLoadingResponse(res, skeleton);
    };
    
    next();
  };
}

/**
 * Standard loading messages for different modules
 */
export const LOADING_MESSAGES = {
  STAFF: {
    OVERVIEW: 'Loading staff overview...',
    TRANSACTIONS: 'Loading staff transactions...',
    DETAILS: 'Loading staff member details...',
    PAYMENTS: 'Loading staff payments...'
  },
  PAYMENTS: {
    OVERVIEW: 'Loading payment overview...',
    SUMMARY: 'Loading payment summary...',
    HISTORY: 'Loading payment history...',
    DETAILS: 'Loading payment details...'
  },
  DUES: {
    OVERVIEW: 'Loading dues overview...',
    SUMMARY: 'Loading dues summary...',
    HISTORY: 'Loading dues history...',
    DETAILS: 'Loading due details...'
  },
  WALLET: {
    OVERVIEW: 'Loading wallet overview...',
    BALANCE: 'Loading wallet balance...',
    TRANSACTIONS: 'Loading wallet transactions...',
    HISTORY: 'Loading transaction history...'
  },
  DASHBOARD: {
    MAIN: 'Loading dashboard...',
    STATS: 'Loading statistics...',
    SUMMARY: 'Loading summary data...'
  }
} as const;
