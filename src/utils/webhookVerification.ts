import crypto from 'crypto';
import logger from './logger';

/**
 * Verify Plaid webhook signature for security
 * Based on Plaid's webhook verification documentation
 */
export function verifyPlaidWebhookSignature(
  requestBody: string,
  signature: string,
  webhookSecret: string
): boolean {
  try {
    if (!signature || !webhookSecret) {
      logger.warn('Missing signature or webhook secret for verification');
      return false;
    }

    // Create HMAC SHA256 hash
    const hmac = crypto.createHmac('sha256', webhookSecret);
    hmac.update(requestBody, 'utf8');
    const expectedSignature = hmac.digest('hex');

    // Compare signatures
    const isValid = crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );

    if (!isValid) {
      logger.warn('Webhook signature verification failed', {
        expectedSignature,
        receivedSignature: signature
      });
    }

    return isValid;
  } catch (error) {
    logger.error('Error verifying webhook signature', { error });
    return false;
  }
}

/**
 * Extract signature from request headers
 */
export function extractWebhookSignature(req: any): string | null {
  const signature = req.headers['plaid-verification'] || 
                   req.headers['x-plaid-signature'] ||
                   req.headers['plaid-signature'];
  
  return signature || null;
}

/**
 * Get request body as string for signature verification
 */
export function getRequestBody(req: any): string {
  if (typeof req.body === 'string') {
    return req.body;
  }
  
  // If body is already parsed as JSON, stringify it
  return JSON.stringify(req.body);
} 